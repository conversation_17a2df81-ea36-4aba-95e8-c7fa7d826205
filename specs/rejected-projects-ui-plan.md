# Rejected Projects UI Implementation Plan

## Overview

This specification outlines the comprehensive UI changes required to implement rejected project discoverability and editing features in the SlydeShow application. The plan follows the backend implementation that includes resubmission functionality and rejection history tracking. Note: the previous `showMyRejected` toggle was removed in favor of a simpler behavior: the `getAll` endpoint no longer accepts `showMyRejected` and the "all" view excludes rejected projects by default; clients should request `status: 'rejected'` to view rejected projects.

## Backend Implementation Status ✅

The following backend features have been implemented:

### Projects Router (`projects.router.ts`)

- ✅ Removed `showMyRejected` parameter from `getAll` endpoint; `status === 'all'` now excludes rejected projects by default
- ✅ Added `resubmitForApproval` mutation for editing and resubmitting rejected projects
- ✅ Updated query logic to compute dynamic statuses (active/completed) and handle rejected project visibility consistently

### Approvals Router (`approvals.router.ts`)

- ✅ Added `getRejectedProjects` endpoint to fetch rejected projects with approval details
- ✅ Added `getRejectionHistory` endpoint to view all rejection history for a project
- ✅ Both endpoints respect user permissions (admin sees all, creators see their own)

### Schemas & Types

- ✅ Added `rejected` status to `ProjectCreateSchema`
- ✅ Created `ProjectResubmissionSchema` for resubmission functionality
- ✅ Added comprehensive TypeScript types for all new functionality
- ✅ Enhanced query hooks with proper error handling and cache invalidation

## UI Implementation Plan

### **Phase 1: Core UI Updates** (High Priority - MVP)

#### **1.1 Project Status Badge Updates**

**File**: `src/components/projects/project-status-badge.tsx`

**Changes Required**:

```typescript
// Add to getProjectStatusVariant function
case PROJECT_STATUS.REJECTED:
  return "red";

// Add to statusIcons object
[PROJECT_STATUS.REJECTED]: <IconX className="h-3.5 w-3.5" />,
```

**Implementation Notes**:

- Import `IconX` from `@tabler/icons-react`
- Ensure red variant exists in Badge component
- Update TypeScript types to include rejected status

#### **1.2 Project Card Updates**

**File**: `src/components/projects/project-card.tsx`

**Changes Required**:

```typescript
// Add to cardBorderColors
[PROJECT_STATUS.REJECTED]: "border-t-red-500",

// Update getDateText function
if (project.status === PROJECT_STATUS.REJECTED) {
  return `Rejected on ${formatDate(project.updatedAt)}`;
}

// Update getActionButtonLabel function
if (project.status === PROJECT_STATUS.REJECTED) {
  return "Resubmit";
}

// Update getActionButtonHref function
if (project.status === PROJECT_STATUS.REJECTED) {
  return `/${orgSlug}/projects/${project.id}?action=resubmit`;
}

// Update getActionButtonIcon function
if (project.status === PROJECT_STATUS.REJECTED) {
  return <IconRefresh size={16} />;
}
```

**Implementation Notes**:

- Import `IconRefresh` from `@tabler/icons-react`
- Consider adding rejection reason display if available from latest approval
- Add visual indicator for rejected status (red border, icon, etc.)

#### **1.3 Projects View Filter Enhancement**

**File**: `src/components/projects/projects-view.tsx`

**Changes Required / Implemented**:

```typescript
// Add to status filter dropdown (make sure Rejected is an option)
<SelectItem value="rejected">Rejected</SelectItem>

// NOTE: The `showMyRejected` toggle was intentionally removed. The application now uses the
// status dropdown as the single source of truth for viewing rejected projects. When the
// status is set to 'all', rejected projects are excluded by default. To view rejected
// projects, users should select the 'Rejected' status from the dropdown.

// useInfiniteProjects call should no longer pass showMyRejected
const projects = useInfiniteProjects({
  organizationId: organization?.data?.id ?? "",
  searchString,
  status,
  myProjects: myProjects === "true",
});
```

**Implementation Notes**:

- Use the status dropdown to surface rejected projects explicitly
- No toggle required; simplifies the UI and reduces localStorage flags
- Ensure the 'Rejected' option is clearly labeled and documented in the UI

#### **1.4 Project Card Actions Menu Updates**

**File**: `src/components/projects/project-card-actions-menu.tsx`

**Changes Required**:

```typescript
// Import resubmit mutation
import { useProjectResubmitMutation } from "@/queries/project.queries";

// Add resubmit functionality
const resubmitMutation = useProjectResubmitMutation();

// Add menu item for rejected projects
{status === PROJECT_STATUS.REJECTED && (
  <DropdownMenuItem onClick={onResubmitProject} disabled={disabled}>
    <IconRefresh className="size-4" />
    <span>Resubmit for Approval</span>
  </DropdownMenuItem>
)}

// Add resubmit handler
async function onResubmitProject() {
  await resubmitMutation.mutateAsync({
    projectId: projectId,
    // Additional fields can be added in a dialog
  });
}
```

**Implementation Notes**:

- Consider opening a resubmission dialog for editing project details
- Disable certain actions for rejected projects (e.g., preview might not work)
- Add proper loading states and error handling

#### **1.5 Project Resubmission Dialog (New Component)**

**File**: `src/components/projects/project-resubmission-dialog.tsx`

**Component Structure**:

```typescript
interface ProjectResubmissionDialogProps {
  open: boolean;
  onClose: () => void;
  project: Project;
  rejectionHistory?: RejectionHistoryItem[];
}

export function ProjectResubmissionDialog({
  open,
  onClose,
  project,
  rejectionHistory,
}: ProjectResubmissionDialogProps) {
  // Form state for editing project details
  // Display rejection history/comments
  // Submit resubmission with updated details
}
```

**Features**:

- Edit project name, dates, locations
- View rejection history/comments prominently
- Form validation and error handling
- Submit updated project for approval
- Success/error messaging

### **Phase 2: Enhanced Views and Navigation** (Medium Priority)

#### **2.1 Project Detail View Updates**

**File**: `src/components/projects/project-view.tsx`

**Changes Required**:

```typescript
// Add rejection banner for rejected projects
{project.status === PROJECT_STATUS.REJECTED && (
  <Alert variant="destructive" className="mb-6">
    <IconX className="h-4 w-4" />
    <AlertTitle>Project Rejected</AlertTitle>
    <AlertDescription>
      This project was rejected during the approval process.
      View rejection details below and make necessary changes before resubmitting.
    </AlertDescription>
  </Alert>
)}

// Add resubmit button for rejected projects
{project.status === PROJECT_STATUS.REJECTED && (
  <Button onClick={handleResubmit} className="mb-4">
    <IconRefresh className="mr-2 h-4 w-4" />
    Resubmit for Approval
  </Button>
)}

// Add link to view rejection history
{project.status === PROJECT_STATUS.REJECTED && (
  <Button variant="outline" onClick={showRejectionHistory}>
    View Rejection History
  </Button>
)}
```

**Implementation Notes**:

- Show rejection details prominently at the top
- Disable slide editing for rejected projects until resubmitted
- Add visual indicators throughout the interface for rejected status

#### **2.2 Enhanced Approvals View**

**File**: `src/components/approvals/approvals-view.tsx`

**Changes Required**:

```typescript
// Add rejected tab to status filter
<SelectItem value="rejected">Rejected</SelectItem>

// Update project action buttons for rejected items
{approval.status === "rejected" && approval.project.creatorId === currentUserId && (
  <Button
    variant="outline"
    onClick={() => handleResubmit(approval.project)}
    size="sm"
  >
    Resubmit
  </Button>
)}

// Add "View Rejected Projects" section for admins
{userRole === "admin" && (
  <Card className="border-red-200 bg-red-50">
    <CardHeader>
      <CardTitle className="text-red-800">Rejected Projects</CardTitle>
    </CardHeader>
    <CardContent>
      <Button variant="outline" href={`/${orgSlug}/projects?status=rejected`}>
        View All Rejected Projects
      </Button>
    </CardContent>
  </Card>
)}
```

#### **2.3 Rejection History Component (New)**

**File**: `src/components/projects/rejection-history.tsx`

**Component Structure**:

```typescript
interface RejectionHistoryProps {
  projectId: string;
  open: boolean;
  onClose: () => void;
}

export function RejectionHistory({
  projectId,
  open,
  onClose,
}: RejectionHistoryProps) {
  const rejectionHistory = useRejectionHistory(projectId);

  // Display timeline of all rejections
  // Show approver comments for each rejection
  // Links to historical approval records
  // Export/print functionality
}
```

**Features**:

- Timeline view of all rejections
- Approver details and comments
- Timestamps and rejection reasons
- Responsive design for mobile viewing

### **Phase 3: Advanced Features** (Low Priority - Polish)

#### **3.1 Dedicated Rejected Projects View**

**File**: `src/components/projects/rejected-projects-view.tsx`

**Features**:

- Dedicated page for browsing rejected projects
- Advanced filtering (by rejection date, approver, etc.)
- Bulk resubmission actions
- Export rejected projects data
- Statistics on rejection rates

#### **3.2 Enhanced Notification System**

**Integration Points**:

- Toast messages for resubmission success/failure
- Email notifications for rejection status changes
- In-app notifications for rejection updates
- Push notifications for mobile (future)

### **Phase 4: New Routes and Navigation**

#### **4.1 New Routes**

```typescript
// Dedicated rejected projects page
/[slug]/projects/rejected

// Project rejection history page
/[slug]/projects/[id]/rejection-history

// Enhanced project detail with rejection info
/[slug]/projects/[id]?view=rejection-details
```

#### **4.2 Navigation Updates**

- Add "Rejected Projects" link to project navigation
- Add breadcrumbs for rejection-related pages
- Update sidebar navigation for admin users

### **Phase 5: User Experience Enhancements**

#### **5.1 Accessibility**

- Screen reader support for rejection status
- High contrast mode for rejected project indicators
- Keyboard navigation for all new dialogs and actions
- ARIA labels for rejection-related elements

#### **5.2 Mobile Responsiveness**

- Mobile-optimized rejection details display
- Touch-friendly resubmission actions
- Responsive rejection history timeline
- Mobile drawer for rejection comments

#### **5.3 Loading and Error States**

- Skeleton loading for rejection history
- Error boundaries for resubmission failures
- Retry mechanisms for failed API calls
- Graceful degradation for offline scenarios

### **Phase 6: Testing and Quality Assurance**

#### **6.1 Component Testing**

- Unit tests for all new components
- Integration tests for resubmission flow
- Visual regression tests for rejection UI states
- Accessibility testing with screen readers

#### **6.2 User Acceptance Testing**

- Test rejected project discoverability
- Validate resubmission workflow
- Confirm permission-based visibility
- Performance testing with large datasets

#### **6.3 Edge Case Handling**

- Projects with multiple rejections
- Concurrent resubmission attempts
- Permission changes during resubmission
- Network failures during critical operations

## Implementation Priority

### **High Priority (MVP) - Week 1-2**

1. Project status badge rejected support (1.1)
2. Project card rejected status display (1.2)
3. Projects view filter for rejected (1.3) — Completed: `showMyRejected` removed; status dropdown drives rejected view
4. Basic action menu updates (1.4)
5. Project resubmission dialog (1.5)

### **Medium Priority - Week 3-4**

6. Project detail view rejection banner (2.1)
7. Enhanced approvals view (2.2)
8. Rejection history component (2.3)

### **Low Priority (Polish) - Week 5+**

9. Dedicated rejected projects view (3.1)
10. Advanced notification system (3.2)
11. Analytics and reporting (3.3)
12. Enhanced routing and navigation (4.1, 4.2)

## Technical Considerations

### **Performance**

- Lazy load rejection history to avoid unnecessary API calls
- Implement pagination for large rejection datasets
- Use React.memo for expensive rejection-related components
- Optimize bundle size by code-splitting rejection features

### **Backwards Compatibility**

- Ensure existing project workflows continue to function
- Maintain API backwards compatibility for external integrations
- Graceful handling of projects without rejection data
- Migration strategy for existing rejected projects

## Success Metrics

### **User Experience**

- Reduction in support tickets related to rejected projects
- Increased project resubmission rates
- Improved user satisfaction scores
- Faster time-to-resolution for rejected projects

### **Technical**

- Zero breaking changes to existing functionality

### **Business**

- Improved approval workflow efficiency
- Better visibility into rejection patterns
- Enhanced admin oversight capabilities
- Reduced project abandonment rates

## Conclusion

This implementation plan provides a comprehensive approach to adding rejected project discoverability and editing features to the SlydeShow UI. The phased approach ensures that critical functionality is delivered first, while advanced features can be implemented iteratively based on user feedback and business priorities.

The plan maintains consistency with existing UI patterns, ensures proper error handling and accessibility, and provides a solid foundation for future enhancements to the project approval workflow.
