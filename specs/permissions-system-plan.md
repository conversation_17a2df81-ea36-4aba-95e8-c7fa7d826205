## Granular Permissions & Publishing Control Implementation Plan

### Document Version

| Field           | Value                                                                                                                                                      |
| --------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Status          | Draft (Initial Commit)                                                                                                                                     |
| Last Updated    | 2025-09-10                                                                                                                                                 |
| Owner           | Platform / Auth                                                                                                                                            |
| Related Systems | better-auth (organization + dynamic access control), tRPC routers (projects, approvals), Prisma (Organization.metadata, Member), Frontend permission hooks |

---

## 1. Overview

The current application uses a coarse, role-based permission model (admin, manager, member) enforced mostly through ad-hoc checks (e.g. `userRole === 'admin'`). This limits flexibility and makes it hard for organizations to grant non-admin users specific deployment / publishing rights. This plan introduces a structured, extensible permission system built on top of **better-auth** dynamic access control while maintaining backward compatibility and a low-friction default configuration.

The primary near-term business goal: Allow organizations to safely delegate the ability to publish (activate) projects without granting full administrative control.

Secondary goals: Establish a durable pattern for future fine‑grained actions (approving, assigning locations, managing slides, billing, reporting) with minimal churn.

---

## 2. Core Principles

1. Backward compatible first release (no immediate UI breakage).
2. Server-side enforcement is authoritative (client checks are hints only).
3. Additive overrides only in v1 (orgs can grant extra capabilities; removal / deny lists can wait for v2).
4. Central declarative permission vocabulary (single source of truth).
5. Simple defaults; complexity opt‑in via organization metadata.
6. Progressive rollout (start with publish action, expand incrementally).

---

## 3. Current State (Baseline Audit)

| Aspect                | Observation                                                                          | Risk                              |
| --------------------- | ------------------------------------------------------------------------------------ | --------------------------------- |
| Role definitions      | Hard-coded enum in `organization.types.ts`                                           | Rigid for customization           |
| Permission usage      | Direct boolean derivations in `use-permissions.ts`                                   | Scattered logic                   |
| Server enforcement    | Many mutations (e.g. `publishProject`) lack explicit authorization beyond membership | Potential privilege escalation    |
| Dynamic config        | None                                                                                 | No per-organization nuance        |
| Storage for overrides | Not present                                                                          | Migration friction if added later |

---

## 4. Target Architecture

### 4.1 Permission Statement (Action Vocabulary)

Define a statement map (resource → actions) in a new module `src/libs/auth-permissions.ts` (or `src/auth/permissions.ts`):

Resources (initial scope):

- `project`: create, read, update, delete, submit, approve, publish, assign_locations, manage_slides
- `slide`: upload, reorder, delete, mark_important
- `approval`: view, review, resubmit
- `member`: invite, update_role, remove
- `organization`: update, billing, view_reports
- `location`: read, assign, manage

### 4.2 Base Roles (Static Defaults)

| Role                      | Intent                    | Capabilities (summary)                                                    |
| ------------------------- | ------------------------- | ------------------------------------------------------------------------- |
| member                    | Standard contributor      | project(create/read/update(own)/submit), slide(basic), approval(view own) |
| manager                   | Elevated project operator | member + assign_locations, manage_slides, view_reports                    |
| publisher (new, optional) | Focused deployment role   | project(publish) only (can be granted additionally)                       |
| admin                     | Full control              | All actions                                                               |

> NOTE: We keep original `admin`, `manager`, `member` strings for compatibility; `publisher` is additive and optional.

### 4.3 Dynamic Overrides (Org Level)

Store additive overrides under `Organization.metadata.permissions.overrides` (JSON). Example:

```json
{
  "permissions": {
    "overrides": {
      "manager": { "project": ["publish"] },
      "member": { "project": ["assign_locations"] }
    }
  }
}
```

Merge rule (v1): Effective = BaseRoleActions ∪ OverrideActions. (No subtraction yet.)

### 4.4 Enforcement Flow (Server)

1. Resolve member + role for user & organization.
2. Load organization metadata; extract overrides for that role.
3. Merge → effective capability map.
4. Authorization utility `requirePermission(ctx, orgId, spec)` ensures all requested actions are present; throw `FORBIDDEN` otherwise.
5. Use in sensitive tRPC procedures (start with `publishProject`).

### 4.5 Client Hook Refactor

Replace hard-coded booleans in `usePermissions` with a computed object that:

1. Gets role (existing query).
2. Fetches organization metadata (already available via existing org query path).
3. Merges base + overrides.
4. Exposes a `can(spec)` function and legacy flags (for incremental migration).

---

## 5. Data Model / Persistence Strategy

| Option                       | Description                                | Pros                     | Cons                        | Decision     |
| ---------------------------- | ------------------------------------------ | ------------------------ | --------------------------- | ------------ |
| Org metadata JSON            | Store overrides in `Organization.metadata` | No migration, fast       | JSON querying less granular | ✅ v1        |
| Dedicated overrides table    | Table per role/resource                    | Structured, queryable    | Requires migration day 1    | v2 candidate |
| Per-member JSON capabilities | Field on `Member`                          | User-specific exceptions | Fragmentation, drift        | Avoid v1     |

No schema migration required in v1 (metadata already exists). Add runtime validation for metadata shape.

---

## 6. Incremental Rollout Phases

| Phase | Scope                                                                                | Success Criteria                                      |
| ----- | ------------------------------------------------------------------------------------ | ----------------------------------------------------- |
| 1     | Define statements + base roles; passive utilities only                               | App runs unchanged; no auth regressions               |
| 2     | Server enforce `project.publish` on `publishProject`                                 | Non-admin w/out permission blocked; admin unaffected  |
| 3     | Client hook refactor + backward-compatible flags                                     | UI logic still behaves; tests green                   |
| 4     | Add org-level override (internal API) + seed toggle for granting publish to managers | Managers can publish after toggle; default unaffected |
| 5     | UI: Permissions tab (matrix + reset + grant publish)                                 | Admins can manage overrides visually                  |
| 6     | Extend enforcement to approve, assign locations, role changes                        | All sensitive flows gated                             |
| 7     | Optional: Introduce `publisher` role & assignment UI                                 | Distinct deployment role available                    |
| 8     | Observability: Audit log entries for publish/approve                                 | Traceability for compliance                           |

---

## 7. Detailed Tasks (Initial Delivery: Phases 1–4)

### 7.1 Phase 1 Tasks

- Create `auth-permissions` module: statements + role definitions via `createAccessControl`.
- Export: `ac`, `roles`, helper `buildEffectivePermissions(role, orgOverrides)`.
- Unit tests for merging logic.

### 7.2 Phase 2 Tasks

- Implement `requirePermission` (shared util under `src/server/permissions/`).
- Update `publishProject` mutation: fetch project, call `requirePermission(..., { project: ["publish"] })`.
- Add structured TRPC error (`FORBIDDEN`) message: "Missing permission: project.publish".
- Add test covering allowed vs denied.

### 7.3 Phase 3 Tasks

- Refactor `usePermissions`: compute effective map; maintain legacy booleans.
- Add `can(spec)` function.
- Deprecation console.warn on direct role comparisons in new code paths.

### 7.4 Phase 4 Tasks

- Add tRPC mutation `organizations.updatePermissionOverrides` (admin only) to merge additive overrides.
- Validate payload against white-listed resources/actions.
- Invalidate org queries on success.
- Script / seed util to grant manager publish for internal test org(s).

---

## 8. Permission Resolution Algorithm (v1)

```
Input: baseRolesMap, roleName, orgOverridesJson
1. base = baseRolesMap[roleName] || {}
2. overrides = orgOverridesJson?.overrides?.[roleName] || {}
3. For each resource in (keys(base) ∪ keys(overrides)):
     effective[resource] = setUnion(base[resource], overrides[resource])
4. Return effective
```

Time complexity: O(R + A) where R = resources, A = total action entries. Negligible at current scale.

---

## 9. Security & Risk Considerations

| Risk                                               | Mitigation                                                                              |
| -------------------------------------------------- | --------------------------------------------------------------------------------------- |
| Silent privilege escalation via malformed metadata | Validate + sanitize overrides server-side before persisting                             |
| Client spoofing permissions                        | Server authoritative checks for all sensitive actions                                   |
| Oversight of newly added mutation lacking checks   | Lint rule / code review checklist; optional wrapper `protectedProcedureWithAuth(spec?)` |
| Growing override complexity (subtractive need)     | Introduce deny lists or role cloning in v2 only if demanded                             |
| Performance (extra db lookups)                     | Reuse existing member/org queries already executed for most views                       |

---

## 10. Testing Strategy

| Layer       | Tests                                                                     |
| ----------- | ------------------------------------------------------------------------- |
| Unit        | Permission merge, `hasAllPermissions` utility                             |
| Integration | publishProject allowed vs denied; override enabling publish; admin bypass |
| Regression  | Snapshot old `usePermissions` flags still correct for base roles          |
| Security    | Attempt publish w/out permission returns 403/`FORBIDDEN`                  |

---

## 11. Observability / Metrics (Future Phase)

- Add audit log row (projectId, actorId, action, timestamp, diff summary) for: publish, approve, role change.
- Optional feature flag: `AUDIT_LOG_ENABLED`.
- Later: Prometheus counter / logs search for permission denials.

---

## 12. UI / UX (Phase 5 Preview)

Permissions Tab (Admin only):

- Matrix (Roles × Resources) collapsed groups.
- Filter: quick toggle “Show only deployment-related”.
- Buttons: "Grant publish to managers", "Reset to defaults".
- Read-only badges for actions not yet generally configurable.

Accessibility: Each checkbox labeled with `aria-describedby` referencing action description.

---

## 13. Migration & Rollback

| Step             | Forward Action                | Rollback                                 |
| ---------------- | ----------------------------- | ---------------------------------------- |
| Introduce module | Add file only                 | Remove file; no data changes             |
| Enforce publish  | Add try/catch fallback (logs) | Revert single mutation edit              |
| Overrides API    | Feature flag gate             | Disable flag; existing overrides ignored |

No destructive schema changes in v1 → rollback is low risk.

---

## 14. Future Enhancements (Not in v1 Scope)

- Subtractive overrides (deny lists).
- Time-bound grants (expiration timestamps per override).
- Per-project ACL (shared editing across members/groups).
- Group-based aggregate permissions (reuse existing `Group` model).
- Policy evaluation caching layer (LRU keyed by orgId+memberId+version).
- UI diff viewer for approval vs published version.

---

## 15. Open Questions

| Question                                                            | Current Answer / Assumption                        |
| ------------------------------------------------------------------- | -------------------------------------------------- |
| Should managers implicitly get publish if they created the project? | No (explicit grant preserves control)              |
| Do we need a distinct approval vs publish separation?               | Yes; keep `approve` and `publish` separate actions |
| Will customers want custom role names soon?                         | Defer; gather feedback after base system ships     |
| Is per-member exception required now?                               | Not for initial orgs; avoid complexity             |

---

## 16. Initial Action Item Checklist (Execution)

```markdown
- [x] Phase 1: Add statements & base roles module
- [-] Phase 1: Unit tests for merge logic (intentionally skipped per request)
- [x] Phase 2: Add server `requirePermission` util
- [x] Phase 2: Enforce on publishProject mutation
- [-] Phase 2: Integration test (publish allowed/denied) (skipped per request)
- [x] Phase 3: Refactor usePermissions hook (backward compatible)
- [x] Phase 3: Add `can()` API + legacy flag mapping
- [x] Phase 4: Add overrides tRPC mutation (admin only)
- [x] Phase 4: Validation + sanitization for overrides
- [x] Phase 4: Seed script / internal toggle to grant manager publish
```

---

## 17. Glossary

| Term                  | Definition                                                           |
| --------------------- | -------------------------------------------------------------------- |
| Base Role             | Static predefined set of actions shipped with app                    |
| Override              | Additive extension of a base role’s allowed actions per organization |
| Effective Permissions | Union of base role actions and additive overrides                    |
| Capability Spec       | Object describing required actions (e.g. `{ project: ["publish"] }`) |

---

## 18. Appendix A: Example Effective Permission Resolution

Given:

```
Base(manager) = {
  project: [create, read, update, submit, assign_locations, manage_slides],
  organization: [view_reports]
}
Overrides(manager) = { project: [publish] }
Effective(manager) = {
  project: [create, read, update, submit, assign_locations, manage_slides, publish],
  organization: [view_reports]
}
```

---

## 19. Approval / Sign-off

| Role             | Name  | Date | Notes |
| ---------------- | ----- | ---- | ----- |
| Engineering Lead | _TBD_ |      |       |
| Product          | _TBD_ |      |       |
| Security Review  | _TBD_ |      |       |

---

End of specification.
