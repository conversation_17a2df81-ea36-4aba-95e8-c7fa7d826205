# Consolidate Permissions to Better Auth Dynamic Roles (no back-compat)

## Summary

We will consolidate the authorization model to use Better Auth’s Organization plugin with dynamic access control (dynamic roles) as the single source of truth for role definitions and permissions. Built-in roles (admin, manager, member) remain, and custom roles are managed per-organization via Better Auth APIs. No back-compat is required on this branch.

## Goals

- Keep current behavior for built-in roles (admin, manager, member). All pages that are not the permissions page should remain and function the same.
- Single source of truth for roles/permissions: Better Auth dynamic roles + a canonical AC (Access Control) statement map
- Keep built-ins (admin, manager, member) as-is; add org-scoped dynamic roles via Better Auth
- Centralize permission checks; eliminate org.metadata-based role/override logic
- Provide UI and APIs to create/update/delete roles using Better Auth
- Prefer the simplest viable implementation (remove dead code and redundant layers; favor straightforward functions and descriptive names).
- Comments must be local and purposeful: explain what the code does and why it is needed; avoid references to discussions, tickets, or plans.

## Non-Goals

- Migrating existing prod data (no back-compat required here)
- Supporting legacy per-role overrides stored in org metadata

## Current State (at start)

- AC vocabulary centralized in `src/libs/auth-permissions.ts` as STATEMENTS and `ac`.
- Server better-auth configured; we will ensure `organization({ ac, dynamicAccessControl: { enabled: true } })` is on.
- Client uses `organizationClient({ ac, dynamicAccessControl: { enabled: true } })`.
- Some custom role logic exists via `organization.metadata` (JSON) and server mutations using our sanitizer.
- Server enforcement guard `require-permission` computes effective permissions via local helpers.

## Target Architecture

- Canonical vocabulary: `STATEMENTS` and `ac` in `src/libs/auth-permissions.ts`.
- Built-ins: admin, manager, member created from `ac`.
- Dynamic roles: stored and managed by Better Auth org plugin; queried via Better Auth APIs.
- Authorization: server guard resolves permissions from Better Auth roles (including dynamic ones) using `ac` to validate; no reads from `organization.metadata`.
- UI: role management (CRUD) via Better Auth; invite/change-role dropdowns list Better Auth roles.

## Key Decisions

1. Source of truth for roles
   - Built-ins are static in code (using `ac.newRole`).
   - All org-specific custom roles live in Better Auth (dynamic roles).
2. No org-level “overrides” concept
   - Instead of additive overrides per role in metadata, we update the dynamic role’s permission map at runtime.
3. Server-side permission checks
   - Resolve the current member’s role(s) and load the role’s permission map from Better Auth, then check against `ac` and requested capability.
4. Reserved role names
   - Prevent creating dynamic roles that shadow built-ins: `admin`, `manager`, `member`.
5. Optional multi-role support
   - Better Auth supports arrays of roles. Policy decision: start with single role per member (current UX), keep code ready to accept arrays later.

## Engineering Guidelines: Simplicity & Comments

- Simplicity first
  - Prefer minimal, direct implementations; remove dead code, unused helpers, and redundant abstractions.
  - Keep functions small with descriptive names; avoid premature generalization.
- Comments
  - Place comments adjacent to the logic they describe.
  - Explain what the code does and why it is needed; do not reference discussions, tickets, or plans.
  - Use short JSDoc for public helpers; use inline comments for non-obvious logic.
- Boundaries
  - Centralize the permission vocabulary and checks; avoid parallel sources of truth.
  - Limit cross-module coupling; keep role/permission logic in the auth and permissions layers.
- Errors & logs
  - Make error messages explicit and actionable; avoid leaking internals.
  - Log succinctly at boundaries (API calls, guard decisions) when helpful for debugging.
- Tests
  - Cover built-in and dynamic roles with clear allow/deny assertions at the guard boundary.

## Implementation Plan

Phase 0 – Prereqs (Status: should be complete or trivial)

- Server: `organization({ ac, dynamicAccessControl: { enabled: true } })`
- Client: `organizationClient({ ac, dynamicAccessControl: { enabled: true } })`

Phase 1 – Server authorization uses Better Auth roles

- Add a small adapter in `src/server/permissions/role-source.ts`:
  - `getRolePermissionMapFromBA(orgId: string, roleName: string): Promise<Record<string, string[]>>`
  - Use Better Auth server APIs (e.g., get role by name or list) to resolve dynamic role permissions.
- Update `evaluateEffectivePermissions` (or replace it) to:
  - If role is built-in: use built-in base from `ac.newRole` definitions (or mirror as a BA role; either is fine because vocab is unified).
  - Else: fetch dynamic role map via Better Auth adapter.
  - Remove any reads of `organization.metadata.permissions.*`.
- Update `require-permission` to call the new resolution path.

Phase 2 – Remove metadata-based custom roles and overrides

- Replace `upsertCustomRoleBase` mutation: implement thin pass-through wrappers to Better Auth APIs:
  - `createRole`, `updateOrgRole`, `deleteOrgRole`, `getOrgRole`, `listOrgRoles`.
- Remove or deprecate any usage of `organization.metadata.permissions.roles` and `permissions.overrides`.
- Keep `sanitizeOverrides` only for UI-side convenience if needed; server-side validation should rely on Better Auth + `ac`.

Phase 3 – Client integrations

- Add wrappers in `src/libs/auth-client.ts` for Better Auth role APIs (create/update/delete/list/get role) for convenient usage across the app.
- Integrate queries/mutations that touch `organization.metadata.permissions` to use the new BA APIs.
- Update invite/change-role flows to source role options from `listOrgRoles`.
- Ensure the existing `inviteOrganizationMember` helper supports arbitrary role names (already done).

Phase 4 – UI updates

- New Roles admin screen:
  - List roles from Better Auth (built-ins + dynamic).
  - Create role (name + permissions from AC vocab), Update permissions, Rename, Delete.
  - Enforce reserved names and validate resource/actions against `STATEMENTS`.
- Permission matrix will go away and will create a ui for creating new roles. Current Roles will be able to be viewed via a dialog or section on the page. (Update the current permissions page no need to create a new )
- Update Invites and Member Management dropdown to reflect roles from Better Auth.

Phase 5 – Cleanup & hardening

- Remove any remaining code paths reading/writing `organization.metadata.permissions.*`.
- Optionally add a one-off script to remove stale keys from `organization.metadata` in the DB.
- Add guards for maximum roles per org (set `maximumRolesPerOrganization` if desired).
- Simplify the `auth-permissions.ts` module to only contain the AC vocabulary and helpers that directly use it. The code should be simplified and well commented and easy to understand. Don't over engineer.
- Will not be adding unit tests; integration tests/manual verification only.

## Example Config (server & client)

Server (already in place or to be verified):

```ts
import { organization } from "better-auth/plugins";
import { ac } from "@/libs/auth-permissions";

organization({
  ac,
  dynamicAccessControl: { enabled: true },
});
```

Client (already in place or to be verified):

```ts
import { organizationClient } from "better-auth/client/plugins";
import { ac, admin, manager, member } from "@/libs/auth-permissions";

organizationClient({
  ac,
  roles: { admin, manager, member },
  dynamicAccessControl: { enabled: true },
});
```

Server adapter example (to implement):

```ts
export async function getRolePermissionMapFromBA(orgId: string, role: string) {
  // Pseudocode: call Better Auth server to fetch role by name in org
  // validate returned map against `ac`/STATEMENTS if needed
  return permissionMap; // Record<string, string[]>
}
```

## Testing Strategy

- Unit tests - Not needed but useful for reference
  - `require-permission` with each built-in role
  - `require-permission` with a dynamic role resolved via adapter (mock BA response)
- Integration tests - Done Manually
  - Create dynamic role -> assign to member via invite -> hit protected endpoints -> expect allow/deny per permissions
  - Update role permissions -> re-check access reflects changes immediately
- UI tests - Done Manually
  - Roles screen CRUD operations and role list used in invites/change-role

## Risks & Mitigations

- Name collisions: disallow creating roles named `admin`, `manager`, `member`.
- Excess roles per org: configure `maximumRolesPerOrganization` if needed.
- Multi-role complexity: keep single-role UX for now; evaluate multi-role later.
- Latency on permission checks if fetching roles frequently: cache role maps per org/role with short TTL on server.

## Acceptance Criteria

- Server authorization no longer reads `organization.metadata` for roles or overrides.
- All role CRUD operations use Better Auth APIs; UI reflects these roles.
- Built-ins remain intact and functionally unchanged.
- End-to-end enforcement works for dynamic roles (create -> assign -> enforce).
- Code is simplified: unnecessary abstractions removed and only essential helpers retained.
- Comments are clear and localized: they describe what the code does and why; they do not reference external suggestions or this plan.
