## Consolidate Permissions: Better-Auth As Single Source of Truth

### Document Version

| Field           | Value                                                                                                                                              |
| --------------- | -------------------------------------------------------------------------------------------------------------------------------------------------- |
| Status          | Draft                                                                                                                                              |
| Last Updated    | 2025-09-10                                                                                                                                         |
| Owner           | Platform / Auth                                                                                                                                    |
| Related Systems | better-auth (core, organization, access), tRPC (organizations, domain routers), Prisma (Organization.metadata, Member), Frontend permission matrix |

---

## 1) Goal & Non‑Goals

- Goal: Make better-auth’s Access Control (AC) the single source of truth for permission vocabulary (resources/actions) and base role capabilities. Eliminate drift between custom RBAC utilities and better-auth.
- Keep: Tenant-level additive overrides (per role) stored in organization metadata for v1.
- Enforce: All sensitive operations authorized server-side via a single utility that evaluates AC + overrides.
- Non‑goals (v1): Deny/subtractive permissions; instance/attribute-scoped permissions; custom role names; full policy editor.

---

## 2) Current State (summary)

- Custom RBAC module `src/libs/auth-permissions.ts` defines:
  - STATEMENTS (vocabulary), BASE_ROLE_PERMISSIONS, `buildEffectivePermissions`, `hasAllPermissions`, plus a second AC config (createAccessControl) with a reduced statement set.
- Client passes `ac` and static roles to `organizationClient` in `src/libs/auth-client.ts`.
- Server enforcement still relies on custom utilities (`require-permission.ts` uses `buildEffectivePermissions` + `hasAllPermissions`).
- Permission Matrix UI (`permission-matrix.tsx`) renders from STATEMENTS and writes overrides via `useUpdatePermissionOverridesMutation`.

Problem: Two policy sources (custom + better-auth AC) can drift; vocabulary not unified.

---

## 3) Target Architecture

- Single policy source: better-auth AC (`ac`) created from a canonical `statement` map containing the full vocabulary (merge today’s STATEMENTS into AC).
- Base roles: defined exclusively through `ac.newRole` (admin, manager, member). Optionally keep virtual roles later.
- Overrides (v1): Still persisted in `Organization.metadata.permissions.overrides`, but sanitized strictly against `ac` statements.
- Evaluation: Server computes effective permissions by combining `ac` role statements with sanitized overrides. All permission checks go through a single server utility.
- UI: Permission Matrix reads vocabulary from `ac` (not STATEMENTS) and marks Base vs Override using role definitions from `ac` + org overrides.

---

## 4) Work Plan (incremental, low‑risk)

Phase 1 — Unify Vocabulary and Roles (no behavior change)

1. Expand `src/libs/auth-permissions.ts` AC `statement` to include the full current vocabulary:
   - project: create, read, update, delete, submit, approve, publish
   - approval: review - (All users can view their own requests, and resubmit rejected requests)
   - member: invite, update_role, remove
   - organization: update
   - location: read, assign, manage
2. Define roles solely via `ac.newRole` to reflect today’s base capabilities for member, manager, admin.
3. Mark legacy exports as deprecated (JSDoc): STATEMENTS, BASE_ROLE_PERMISSIONS, `buildEffectivePermissions`, `hasAllPermissions`, `legacyFlagsFromEffective`.
4. Add helper getters that expose `ac.statements` and role action maps for UI and server.

Phase 2 — Centralize Server Authorization 5. Implement `evaluateEffectivePermissions(role: string, orgMeta: any)` that:

- Pulls base actions from the `ac` role definition
- Merges with sanitized overrides (see Phase 3)
- Returns Resource->Actions map

6. Replace usages of `buildEffectivePermissions` and `hasAllPermissions` in:
   - `src/server/permissions/require-permission.ts`
   - Any domain routers doing permission checks
7. Keep function signature compatibility for `requirePermission({ ctx, organizationId, spec })` but implement with AC.

Phase 3 — Override Sanitization via AC 8. Create a single sanitization util `sanitizeOverrides(overrides, acStatements)` used by both server (enforce) and server mutation (persist):

- Drop unknown resources/actions
- Deduplicate actions
- Ensure additive only

9. Update `organizations.updatePermissionOverrides` tRPC resolver to use `sanitizeOverrides` before saving to metadata.
10. Update invalidation and logging stays as-is.

Phase 4 — UI Migration (Permission Matrix) 11. Switch matrix to render resources/actions from `ac.statements`. 12. Compute base role actions from AC role definitions; compute effective via Phase 2 helper. 13. Keep current additive-only UX; continue using the same mutation for overrides. 14. Memoize per-role effective/base maps to reduce re-renders.

Phase 5 — Cleanup & Deprecations 15. Replace any remaining direct imports of STATEMENTS/BASE_ROLE_PERMISSIONS with AC-based helpers. 16. Keep deprecated exports for one release behind a feature flag (e.g., PERMISSIONS_AC_SOURCE_OF_TRUTH). 17. Update internal docs and linters/codeowners to catch reintroduction of duplicate sources.

---

## 5) Module‑Level Changes

- src/libs/auth-permissions.ts
  - Make `statement` the canonical vocabulary (superset of current STATEMENTS).
  - `ac = createAccessControl(statement)` remains.
  - Define `admin`, `manager`, `member` solely via `ac.newRole` with actions mirroring today’s base behavior.
  - Export helpers:
    - `getStatements(): Record<Resource, string[]>` → from `ac`.
    - `getRoleBaseActions(role: string): Record<string, string[]>` → from AC role.
    - `evaluateEffectivePermissions(role, orgMeta)` → AC base + sanitized overrides.
  - Mark legacy exports deprecated (keep for transition).

- src/server/permissions/require-permission.ts
  - Replace `buildEffectivePermissions` + `hasAllPermissions` with AC-based evaluation + check.
  - Keep same error semantics (TRPCError FORBIDDEN) and message format.

- src/components/settings/permissions/permission-matrix.tsx
  - Render rows from `getStatements()`.
  - Base/Override badges from AC role base vs. org metadata overrides.
  - Continue using `useUpdatePermissionOverridesMutation`.

- src/queries/organization.queries.ts (server resolver for updatePermissionOverrides)
  - Ensure server-side uses `sanitizeOverrides` against `getStatements()`.

- src/libs/auth-client.ts
  - No breaking changes; continues to pass `ac` and roles to `organizationClient`.

---

## 6) Data Model & Backward Compatibility

- Storage: Keep overrides in `Organization.metadata.permissions.overrides`.
- Validation: All writes sanitized against `getStatements()`.
- Computation: AC role base ∪ sanitized overrides.
- No schema migrations required.

---

## 7) Acceptance Criteria

- Single vocabulary definition: Removing STATEMENTS does not break UI; matrix renders from AC.
- Server checks use AC-derived evaluation; removing `buildEffectivePermissions` does not change behavior for current roles.
- Overrides that include unknown actions/resources are ignored with a warning; no privilege escalation.
- Admin can grant an override (e.g., project.publish to manager) and the change is enforced server-side and reflected in UI.

---

## 8) Testing Plan

- Unit
  - sanitizeOverrides: drops unknowns, dedupes, preserves known actions.
  - evaluateEffectivePermissions: base ∪ overrides union logic.
- Integration
  - Permission‑gated mutation (e.g., publish) denied/allowed based on role + override.
  - UI matrix shows Base vs Override correctly and toggling calls API as expected.
- Regression
  - Legacy flags (if still referenced) map correctly from AC evaluation.

---

## 9) Rollout & Flags

- Feature flag PERMISSIONS_AC_SOURCE_OF_TRUTH
  - When on: UI + server use AC helpers.
  - When off: fallback to existing utilities.
- Rollout order: server first (hidden), then UI.
- Monitor auth errors after release; add temporary verbose logging for denials.

---

## 10) Risks & Mitigations

- Drift during migration → Mitigation: temporary wrappers that delegate to AC; code search CI rule to block new STATEMENTS usage.
- Missing vocabulary items in AC → Mitigation: merge full current set into AC during Phase 1.
- Performance concerns in matrix → Mitigation: memoization per role; precompute effective maps.

---

## 11) Open Questions

- Should we expose a read-only effective permissions endpoint for clients? (Helps SSR/CSR consistency.)
- Do we want to surface AC role definitions in an internal admin page for quick diffing?
- Timeline to remove deprecated exports entirely?

---

## 12) Next Steps (Execution Checklist)

- [ ] Phase 1: Expand AC statement to full vocabulary; align role definitions
- [ ] Phase 2: Replace server checks with AC evaluation helper
- [ ] Phase 3: Centralize override sanitization; use in write + read paths
- [ ] Phase 4: Switch matrix to AC vocabulary + base/override from AC
- [ ] Phase 5: Deprecate/remove duplicate sources; enable feature flag by default
