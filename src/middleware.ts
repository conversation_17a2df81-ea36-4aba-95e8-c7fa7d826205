import {
  apiAuthPrefix,
  APP_ROUTES,
  authRoutes,
  publicRoutes,
  trpcPrefix,
} from "@/libs/constants";
import { getSessionCookie } from "better-auth/cookies";
import { NextResponse, type NextRequest } from "next/server";

export default async function authMiddleware(request: NextRequest) {
  const nextUrl = request.nextUrl;
  const sessionCookie = getSessionCookie(request);

  const isLoggedIn = !!sessionCookie;

  const isApiAuthRoute = nextUrl.pathname.startsWith(apiAuthPrefix);
  const isTrpcRoute = nextUrl.pathname.startsWith(trpcPrefix);
  const isPublicRoute = publicRoutes.includes(nextUrl.pathname);
  const isAuthRoute = authRoutes.includes(nextUrl.pathname);

  if (isApiAuthRoute || isTrpcRoute) {
    return NextResponse.next();
  }

  if (isAuthRoute) {
    if (isLoggedIn) {
      console.log("Logged in, redirecting to dashboard");
      return NextResponse.redirect(new URL(APP_ROUTES.ORGANIZATIONS, nextUrl));
    }
    return NextResponse.next();
  }

  if (!isLoggedIn && !isPublicRoute) {
    console.log("Not logged in, redirecting to login");
    return NextResponse.redirect(new URL(APP_ROUTES.LOGIN, nextUrl));
  }

  console.log("Next response next");
  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!.+\\.[\\w]+$|_next).*)", "/", "/(api|trpc)(.*)"],
};
