"use server";

import { PROJECT_STATUS } from "@/libs/constants";
import { db } from "@/server/db";
import { getTodayStartAndEnd } from "@/utils/dates";

/**
 * Gets all slides for a project
 * @param projectId - The ID of the project to fetch slides for
 * @returns Array of slides for the specified project
 */
export async function getSlides(projectId: string) {
  const slides = await db.slide.findMany({
    where: {
      projectId,
      deletedAt: null,
    },
    select: {
      id: true,
      imageUrl: true,
      order: true,
      duration: true,
      isImportant: true,
    },
    orderBy: {
      order: "asc",
    },
  });

  return slides.sort((a, b) =>
    a.order < b.order ? -1 : a.order > b.order ? 1 : 0,
  );
}

/**
 * Gets slides from active projects at a location
 * @param locationId - Location ID to filter projects
 * @param subLocationId - Optional sublocation ID for filtering
 * @returns Array of slides with their details
 */
export async function getLocationSlides(
  locationId: string,
  subLocationId?: string,
) {
  const { todayStart, todayEnd } = getTodayStartAndEnd();

  // Get slides from active projects at the specified location
  const slides = await db.slide.findMany({
    where: {
      project: {
        status: PROJECT_STATUS.APPROVED, // Only approved projects
        startDate: { lte: todayEnd }, // Projects that have started
        endDate: { gte: todayStart }, // Projects that have not ended
        locations: { some: { shortId: locationId } },
        sublocations: { some: { shortId: subLocationId } },
        deletedAt: null,
      },
      deletedAt: null,
    },
    select: {
      id: true,
      imageUrl: true,
      order: true,
      duration: true,
      isImportant: true,
      projectId: true,
      project: {
        select: {
          slideDuration: true,
          createdAt: true, // Need this to sort projects by creation date
        },
      },
    },
    // Sort by project creation date
    orderBy: [{ project: { createdAt: "asc" } }],
    take: 30, // Limit to 30 slides
  });

  // Track when each project was created using a Map for efficient lookups
  const projectOrderMap = new Map<string, Date>();
  slides.forEach((slide) => {
    if (!projectOrderMap.has(slide.projectId)) {
      projectOrderMap.set(slide.projectId, slide.project.createdAt);
    }
  });

  // Sort slides: oldest projects first, then by slide order within each project
  const sortedSlides = slides
    .sort((a, b) => {
      // Sort by project creation date first
      if (a.projectId !== b.projectId) {
        const aProjectDate = projectOrderMap.get(a.projectId)!;
        const bProjectDate = projectOrderMap.get(b.projectId)!;
        return aProjectDate.getTime() - bProjectDate.getTime();
      }

      // Then sort by slide order within the same project (works for fractional indexing order)
      return a.order < b.order ? -1 : a.order > b.order ? 1 : 0;
    })
    // Return only the data we need
    .map((slide) => ({
      id: slide.id,
      imageUrl: slide.imageUrl,
      order: slide.order,
      duration: slide.duration,
      isImportant: slide.isImportant,
      projectId: slide.projectId,
    }));

  return { slides: sortedSlides, project: slides[0]?.project };
}
