import { FILTER_TAKE } from "@/libs/constants";
import {
  GroupAddMemberSchema,
  GroupAddMembersSchema,
  GroupCreateSchema,
  GroupDeleteSchema,
  GroupFindSchema,
  GroupGetByIdSchema,
  GroupGetForMemberSchema,
  GroupGetMembersSchema,
  GroupRemoveMemberSchema,
  GroupUpdateSchema,
} from "@/schemas/group.schemas";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure } from "../trpc";

// Helper to check membership and role
const ensureOrgAdmin = async (ctx: any, organizationId: string) => {
  const member = await ctx.db.member.findFirst({
    where: {
      organizationId,
      userId: ctx.session.user.id,
      deletedAt: null,
    },
  });

  if (!member || member.role !== "admin") {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
};

export const groupsRouter = createTRPCRouter({
  create: protectedProcedure
    .input(GroupCreateSchema)
    .mutation(async ({ ctx, input }) => {
      await ensureOrgAdmin(ctx, input.organizationId);

      return await ctx.db.group.create({
        data: { ...input },
      });
    }),

  getAll: protectedProcedure
    .input(GroupFindSchema)
    .query(async ({ ctx, input }) => {
      const take = input?.take || FILTER_TAKE;

      const where = {
        deletedAt: null,
        organizationId: input.organizationId,
        ...(input.searchString && { name: { contains: input.searchString } }),
      };

      const data = await ctx.db.group.findMany({
        where,
        include: {
          _count: {
            select: {
              groupMembers: {
                where: { deletedAt: null },
              },
            },
          },
        },
        ...(input.cursor && {
          cursor: { id: input.cursor },
          skip: 1,
        }),
        take,
        orderBy: { createdAt: input.sort || "desc" },
      });

      const result = { data, cursor: "" };
      if (data.length < take) return result;
      return { ...result, cursor: data.at(-1)?.id || "" };
    }),

  getById: protectedProcedure
    .input(GroupGetByIdSchema)
    .query(async ({ ctx, input }) => {
      const data = await ctx.db.group.findUnique({
        where: { id: input.id },
        include: {
          _count: { select: { groupMembers: { where: { deletedAt: null } } } },
        },
      });

      if (!data || data.deletedAt) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Group not found" });
      }

      return data;
    }),

  updateById: protectedProcedure
    .input(GroupUpdateSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      const existing = await ctx.db.group.findUnique({ where: { id } });
      if (!existing || existing.deletedAt) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Group not found" });
      }

      await ensureOrgAdmin(ctx, existing.organizationId);

      return await ctx.db.group.update({
        where: { id },
        data: updateData,
      });
    }),

  deleteById: protectedProcedure
    .input(GroupDeleteSchema)
    .mutation(async ({ ctx, input }) => {
      const existing = await ctx.db.group.findUnique({
        where: { id: input.id },
      });
      if (!existing || existing.deletedAt) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Group not found" });
      }

      await ensureOrgAdmin(ctx, existing.organizationId);

      // Soft delete group and its members in a transaction
      return await ctx.db.$transaction([
        ctx.db.group.update({
          where: { id: input.id },
          data: { deletedAt: new Date() },
        }),
        ctx.db.groupMember.updateMany({
          where: { groupId: input.id, deletedAt: null },
          data: { deletedAt: new Date() },
        }),
      ]);
    }),

  addMember: protectedProcedure
    .input(GroupAddMemberSchema)
    .mutation(async ({ ctx, input }) => {
      const { groupId, memberId, organizationId } = input;

      // Validate group and org
      const db = ctx.db;
      const group = await db.group.findUnique({ where: { id: groupId } });
      if (
        !group ||
        group.deletedAt ||
        group.organizationId !== organizationId
      ) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Group not found" });
      }

      await ensureOrgAdmin(ctx, organizationId);

      // Check member exists in org
      const member = await db.member.findUnique({
        where: { id: memberId },
      });

      if (
        !member ||
        member.deletedAt ||
        member.organizationId !== organizationId
      ) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Member not found" });
      }

      // Idempotent create: if exists and not deleted, do nothing; if exists and deleted, revive
      const existing = await db.groupMember.findFirst({
        where: { groupId, memberId },
      });

      if (existing && !existing.deletedAt) {
        return existing;
      }

      if (existing && existing.deletedAt) {
        return await db.groupMember.update({
          where: { id: existing.id },
          data: { deletedAt: null },
        });
      }

      return await db.groupMember.create({
        data: { groupId, memberId },
      });
    }),

  addMembers: protectedProcedure
    .input(GroupAddMembersSchema)
    .mutation(async ({ ctx, input }) => {
      const { groupId, memberIds, organizationId } = input;

      const db = ctx.db;
      const group = await db.group.findUnique({ where: { id: groupId } });
      if (
        !group ||
        group.deletedAt ||
        group.organizationId !== organizationId
      ) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Group not found" });
      }

      await ensureOrgAdmin(ctx, organizationId);

      const created = await db.groupMember.createMany({
        data: memberIds.map((memberId) => ({ groupId, memberId })),
        skipDuplicates: true,
      });

      return { createdCount: created.count };
    }),

  removeMember: protectedProcedure
    .input(GroupRemoveMemberSchema)
    .mutation(async ({ ctx, input }) => {
      const { groupId, memberId, organizationId } = input;

      const db = ctx.db;
      const group = await db.group.findUnique({ where: { id: groupId } });
      if (
        !group ||
        group.deletedAt ||
        group.organizationId !== organizationId
      ) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Group not found" });
      }

      await ensureOrgAdmin(ctx, organizationId);

      // Simplified: just soft delete the membership if it's currently active
      await db.groupMember.updateMany({
        where: { groupId, memberId, deletedAt: null },
        data: { deletedAt: new Date() },
      });

      return { success: true };
    }),

  getMembers: protectedProcedure
    .input(GroupGetMembersSchema)
    .query(async ({ ctx, input }) => {
      const take = input?.take || FILTER_TAKE;

      const data = await ctx.db.groupMember.findMany({
        where: {
          groupId: input.groupId,
          deletedAt: null,
        },
        include: {
          member: {
            include: {
              user: true,
            },
          },
        },
        ...(input.cursor && { cursor: { id: input.cursor }, skip: 1 }),
        take,
        orderBy: { createdAt: input.sort || "desc" },
      });

      const result = { data, cursor: "" };
      if (data.length < take) return result;
      return { ...result, cursor: data.at(-1)?.id || "" };
    }),

  // Get all groups for a member
  getForMember: protectedProcedure
    .input(GroupGetForMemberSchema)
    .query(async ({ ctx, input }) => {
      const take = input?.take || FILTER_TAKE;

      const where = {
        deletedAt: null,
        organizationId: input.organizationId,
        groupMembers: { some: { memberId: input.memberId, deletedAt: null } },
        ...(input.searchString && { name: { contains: input.searchString } }),
      };

      const data = await ctx.db.group.findMany({
        where,
        include: {
          _count: { select: { groupMembers: { where: { deletedAt: null } } } },
        },
        ...(input.cursor && { cursor: { id: input.cursor }, skip: 1 }),
        take,
        orderBy: { createdAt: input.sort || "desc" },
      });

      const result = { data, cursor: "" };
      if (data.length < take) return result;
      return { ...result, cursor: data.at(-1)?.id || "" };
    }),
});
