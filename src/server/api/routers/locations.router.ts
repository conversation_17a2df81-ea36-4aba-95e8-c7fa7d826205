import { FILTER_TAKE } from "@/libs/constants";
import { nanoid } from "@/libs/nanoid";
import { filterSchema } from "@/schemas/api.schemas";
import {
  LocationCreateSchema,
  LocationUpdateSchema,
  SubLocationCreateSchema,
  SubLocationUpdateSchema,
} from "@/schemas/location.schemas";
import { Roles } from "@/types/organization.types";
import { getTodayStartAndEnd } from "@/utils/dates";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const locationsRouter = createTRPCRouter({
  // Location CRUD operations
  create: protectedProcedure
    .input(LocationCreateSchema)
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.location.create({
        data: { ...input, shortId: nanoid(8) },
        include: {
          sublocations: true,
        },
      });
    }),

  getAll: protectedProcedure
    .input(
      z.object({
        organizationId: z.string(),
        state: z.string().optional(),
        ...filterSchema,
      }),
    )
    .query(async ({ ctx, input }) => {
      // Fetch users membership and any active assignments
      const orgMember = await ctx.db.member.findFirst({
        where: {
          organizationId: input.organizationId,
          userId: ctx.session.user.id,
          deletedAt: null,
        },
        include: {
          locationAssignments: {
            where: { deletedAt: null },
            select: { locationId: true },
          },
        },
      });

      const isAdmin = orgMember?.role === Roles.ADMIN;
      const assignedLocationIds =
        orgMember && !isAdmin && orgMember.locationAssignments
          ? orgMember.locationAssignments.map((a) => a.locationId)
          : [];

      const { todayStart, todayEnd } = getTodayStartAndEnd();

      const take = input?.take || FILTER_TAKE;

      const where = {
        organizationId: input.organizationId,
        deletedAt: null,
        ...(input.state &&
          input.state !== "all" && {
            state: { equals: input.state },
          }),
        // If viewer is a non-admin with assigned locations, restrict to those
        ...(assignedLocationIds.length > 0 && {
          id: { in: assignedLocationIds },
        }),
        ...(input.searchString && {
          OR: [
            { name: { contains: input.searchString } },
            { city: { contains: input.searchString } },
            { state: { contains: input.searchString } },
            { address: { contains: input.searchString } },
            { postalCode: { contains: input.searchString } },
          ],
        }),
      };

      const [locations, total] = await Promise.all([
        ctx.db.location.findMany({
          where,
          include: {
            sublocations: {
              where: { deletedAt: null },
              orderBy: { createdAt: "desc" },
            },
            _count: {
              select: {
                projects: {
                  where: {
                    status: "approved", // Only count approved projects that are active
                    startDate: { lte: todayEnd }, // Projects that have started
                    endDate: { gte: todayStart }, // Projects that have not ended
                    deletedAt: null, // Exclude deleted projects
                  },
                },
              },
            },
          },
          orderBy: {
            createdAt: input.sort || "desc",
          },
          take,
          ...(input.cursor && {
            skip: 1,
            cursor: { id: input.cursor },
          }),
        }),
        ctx.db.location.count({ where }),
      ]);

      const hasMore = locations.length === take;
      const cursor = hasMore ? locations[locations.length - 1]?.id : undefined;

      return {
        data: locations,
        cursor,
        total,
      };
    }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.db.location.findUnique({
        where: { id: input.id, deletedAt: null },
        include: {
          sublocations: {
            where: { deletedAt: null },
          },
          projects: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
          _count: {
            select: {
              projects: true,
            },
          },
        },
      });
    }),
  updateById: protectedProcedure
    .input(LocationUpdateSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;
      return await ctx.db.location.update({
        where: { id },
        data: updateData,
        include: {
          sublocations: true,
        },
      });
    }),

  deleteById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.location.update({
        where: { id: input.id },
        data: { deletedAt: new Date() },
      });
    }),

  // SubLocation CRUD operations
  createSubLocation: protectedProcedure
    .input(SubLocationCreateSchema)
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.subLocation.create({
        data: { ...input, shortId: nanoid(8) },
        include: {
          location: true,
        },
      });
    }),

  getSubLocationsByLocationId: protectedProcedure
    .input(z.object({ locationId: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.db.subLocation.findMany({
        where: { locationId: input.locationId, deletedAt: null },
        orderBy: { createdAt: "desc" },
      });
    }),

  getSubLocationById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.db.subLocation.findUnique({
        where: { id: input.id, deletedAt: null },
        include: {
          location: true,
        },
      });
    }),

  updateSubLocationById: protectedProcedure
    .input(SubLocationUpdateSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;
      return await ctx.db.subLocation.update({
        where: { id },
        data: updateData,
        include: {
          location: true,
        },
      });
    }),

  deleteSubLocationById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.subLocation.update({
        where: { id: input.id },
        data: { deletedAt: new Date() },
      });
    }),
});
