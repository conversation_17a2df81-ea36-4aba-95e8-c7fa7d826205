import { FILTER_TAKE, PROJECT_STATUS } from "@/libs/constants";
import { filterSchema } from "@/schemas/api.schemas";
import { ProjectResubmissionSchema } from "@/schemas/approval.schemas";
import {
  ProjectCreateSchema,
  ProjectUpdateSchema,
} from "@/schemas/project.schemas";
import { requirePermission } from "@/server/permissions/require-permission";
import { getTodayStartAndEnd } from "@/utils/dates"; // <-- added import
import { omit } from "@/utils/omit";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

// Utility function to compute dynamic project status
const computeProjectStatus = (project: any) => {
  const { todayStart, todayEnd } = getTodayStartAndEnd();

  if (
    project.status === PROJECT_STATUS.APPROVED &&
    project.startDate &&
    project.endDate
  ) {
    const startDate = new Date(project.startDate);
    const endDate = new Date(project.endDate);

    // Check if project is completed (past end date)
    if (endDate < todayStart) {
      return { ...project, status: PROJECT_STATUS.COMPLETED };
    }

    // Check if project is currently active (within date range)
    if (startDate <= todayEnd && endDate >= todayStart) {
      return { ...project, status: PROJECT_STATUS.ACTIVE };
    }

    // Project is approved but not yet started or no dates set
    return project;
  }

  return project;
};

// Utility function to compute status for an array of projects
const computeProjectsStatus = (projects: any[]) => {
  return projects.map(computeProjectStatus);
};

export const projectsRouter = createTRPCRouter({
  create: protectedProcedure
    .input(z.object({ organizationId: z.string() }).merge(ProjectCreateSchema))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.project.create({
        data: {
          ...input,
          creatorId: ctx.session.user.id,
        },
      });
    }),
  getAll: protectedProcedure
    .input(
      z.object({
        organizationId: z.string(),
        status: z
          .union([z.literal("all"), z.nativeEnum(PROJECT_STATUS)])
          .optional(),
        myProjects: z.boolean().optional(),
        ...filterSchema,
      }),
    )
    .query(async ({ ctx, input }) => {
      const take = input?.take || FILTER_TAKE;

      // If filtering for approved projects only, we need to handle this at the database level
      // to avoid the dynamic status computation converting them to active
      if (input.status === PROJECT_STATUS.APPROVED) {
        const { todayStart, todayEnd } = getTodayStartAndEnd();

        const data = await ctx.db.project.findMany({
          where: {
            deletedAt: null,
            organizationId: input.organizationId,
            status: PROJECT_STATUS.APPROVED,
            ...(input.myProjects && {
              creatorId: ctx.session.user.id,
            }),
            name: {
              contains: input?.searchString,
            },
            // Only include projects that are not currently active
            AND: [
              {
                OR: [
                  // Projects with no start date
                  { startDate: null },
                  // Projects not yet started
                  { startDate: { gt: todayEnd } },
                ],
              },
              {
                OR: [
                  // Projects with no end date
                  { endDate: null },
                  // Projects not yet ended (for future projects)
                  { endDate: { gte: todayStart } },
                ],
              },
            ],
          },
          include: {
            _count: {
              select: {
                slides: {
                  where: { deletedAt: null },
                },
              },
            },
            locations: {
              where: { deletedAt: null },
              select: {
                id: true,
                shortId: true,
                name: true,
                city: true,
                state: true,
              },
            },
            sublocations: {
              where: { deletedAt: null },
              select: {
                id: true,
                shortId: true,
                name: true,
                locationId: true,
              },
            },
          },
          ...(input.cursor && {
            cursor: {
              id: input.cursor,
            },
            skip: 1,
          }),
          take,
          orderBy: { createdAt: "desc" },
        });

        // Return approved projects without status computation
        const result = { data, cursor: "" };
        if (data.length < take) return result;
        return { ...result, cursor: data.at(-1)?.id || "" };
      }

      if (input.status === PROJECT_STATUS.COMPLETED) {
        const { todayStart } = getTodayStartAndEnd();

        const data = await ctx.db.project.findMany({
          where: {
            deletedAt: null,
            organizationId: input.organizationId,
            status: PROJECT_STATUS.APPROVED,
            ...(input.myProjects && {
              creatorId: ctx.session.user.id,
            }),
            name: {
              contains: input?.searchString,
            },
            // Only projects that are past their end date
            endDate: { lt: todayStart },
          },
          include: {
            _count: {
              select: {
                slides: {
                  where: { deletedAt: null },
                },
              },
            },
          },
          ...(input.cursor && {
            cursor: {
              id: input.cursor,
            },
            skip: 1,
          }),
          take,
          orderBy: { createdAt: "desc" },
        });

        // Apply status computation to convert to COMPLETED
        const processedData = computeProjectsStatus(data);
        const result = { data: processedData, cursor: "" };
        if (processedData.length < take) return result;
        return { ...result, cursor: processedData.at(-1)?.id || "" };
      }

      // For all other status filters, use the existing logic
      const whereCondition = {
        deletedAt: null,
        organizationId: input.organizationId,
        ...(input.myProjects && {
          creatorId: ctx.session.user.id,
        }),
        // Exclude rejected projects unless specifically filtering for them
        ...(input.status === "all" && {
          NOT: {
            status: PROJECT_STATUS.REJECTED,
          },
        }),
        name: {
          contains: input?.searchString,
        },
        ...(input.status !== "all" &&
          input.status !== PROJECT_STATUS.ACTIVE && { status: input.status }),
      };

      const data = await ctx.db.project.findMany({
        where: whereCondition,
        include: {
          _count: {
            select: {
              slides: {
                where: { deletedAt: null },
              },
            },
          },
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { createdAt: "desc" },
      });

      // Compute dynamic status for all projects
      // TODO: Clean this up
      let processedData = computeProjectsStatus(data) as {
        status: string;
        name: string;
        id: string;
        deletedAt: Date | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
        startDate: Date | null;
        endDate: Date | null;
        creatorId: string | null;
        _count: {
          slides: number;
        };
      }[];

      // Filter by active status if requested
      if (input.status === PROJECT_STATUS.ACTIVE) {
        processedData = processedData.filter(
          (project) => project.status === PROJECT_STATUS.ACTIVE,
        );
      }

      const result = { data: processedData, cursor: "" };

      if (processedData.length < take) return result;

      return { ...result, cursor: processedData.at(-1)?.id || "" };
    }),
  getAllActive: protectedProcedure
    .input(z.object({ organizationId: z.string(), ...filterSchema }))
    .query(async ({ ctx, input }) => {
      const take = input?.take || FILTER_TAKE;
      const { todayStart, todayEnd } = getTodayStartAndEnd();

      const data = await ctx.db.project.findMany({
        where: {
          deletedAt: null,
          organizationId: input.organizationId,
          status: PROJECT_STATUS.APPROVED, // Only approved projects
          startDate: {
            lte: todayEnd, // Start date is on or before todayEnd
          },
          endDate: {
            gte: todayStart, // End date is on or after todayStart
          },
          name: {
            contains: input?.searchString,
          },
        },
        include: {
          _count: {
            select: {
              slides: {
                where: { deletedAt: null },
              },
            },
          },
          locations: {
            where: { deletedAt: null },
            select: {
              id: true,
              shortId: true,
              name: true,
              city: true,
              state: true,
            },
          },
          sublocations: {
            where: { deletedAt: null },
            select: {
              id: true,
              shortId: true,
              name: true,
              locationId: true,
            },
          },
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { createdAt: "desc" },
      });

      // Convert approved projects within date range to active status
      const processedData = data.map((project) => ({
        ...project,
        status: PROJECT_STATUS.ACTIVE,
      }));

      const result = { data: processedData, cursor: "" };
      if (processedData.length < take) return result;
      return { ...result, cursor: processedData.at(-1)?.id || "" };
    }),
  getAllApprovedActiveAndCompleted: protectedProcedure
    .input(
      z.object({
        organizationId: z.string(),
        memberRole: z.string(),
        ...filterSchema,
      }),
    )
    .query(async ({ ctx, input }) => {
      const take = input?.take || FILTER_TAKE;

      const isAdmin = input.memberRole === "admin";

      const data = await ctx.db.project.findMany({
        where: {
          deletedAt: null,
          organizationId: input.organizationId,
          status: PROJECT_STATUS.APPROVED, // Get all approved projects (regardless of dates)
          ...(!isAdmin && {
            creatorId: ctx.session.user.id,
          }),
          name: {
            contains: input?.searchString,
          },
        },
        include: {
          _count: {
            select: {
              slides: true,
            },
          },
          locations: {
            where: { deletedAt: null },
            select: {
              id: true,
              shortId: true,
              name: true,
              city: true,
              state: true,
            },
          },
          sublocations: {
            where: { deletedAt: null },
            select: {
              id: true,
              shortId: true,
              name: true,
              locationId: true,
            },
          },
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { createdAt: "desc" },
      });

      // Compute dynamic status for all approved projects (some will become active, some completed)
      // TODO: Clean this up
      const processedData = computeProjectsStatus(data) as {
        status: string;
        name: string;
        id: string;
        deletedAt: Date | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
        startDate: Date | null;
        endDate: Date | null;
        creatorId: string | null;
        _count: {
          slides: number;
        };
        locations: {
          id: string;
          shortId: string;
          name: string;
          city: string;
          state: string;
        }[];
        sublocations: {
          id: string;
          shortId: string;
          name: string;
          locationId: string;
        }[];
      }[];

      const result = { data: processedData, cursor: "" };
      if (processedData.length < take) return result;
      return { ...result, cursor: processedData.at(-1)?.id || "" };
    }),
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const data = await ctx.db.project.findUnique({
        where: { id: input.id, deletedAt: null },
        include: {
          _count: {
            select: {
              slides: {
                where: { deletedAt: null },
              },
            },
          },
          locations: {
            where: { deletedAt: null },
            select: {
              id: true,
              shortId: true,
              name: true,
              city: true,
              state: true,
            },
          },
          sublocations: {
            where: { deletedAt: null },
            select: {
              id: true,
              shortId: true,
              name: true,
              locationId: true,
            },
          },
        },
      });

      if (!data) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Project not found",
        });
      }

      // Compute dynamic status
      return computeProjectStatus(data);
    }),
  getRecentProjects: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const data = await ctx.db.project.findMany({
        where: {
          deletedAt: null,
          organizationId: input.organizationId,
          updatedAt: { gt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
        },
        include: {
          _count: {
            select: {
              slides: {
                where: { deletedAt: null },
              },
            },
          },
          locations: {
            where: { deletedAt: null },
            select: {
              id: true,
              shortId: true,
              name: true,
              city: true,
              state: true,
            },
          },
          sublocations: {
            where: { deletedAt: null },
            select: {
              id: true,
              shortId: true,
              name: true,
              locationId: true,
            },
          },
        },
        take: 4,
        orderBy: { createdAt: "desc" },
      });

      // Compute dynamic status for recent projects
      return computeProjectsStatus(data);
    }),
  getMetrics: protectedProcedure
    .input(z.object({ organizationId: z.string(), memberRole: z.string() }))
    .query(async ({ ctx, input }) => {
      const isAdmin = input.memberRole === "admin";

      const [teamMembers, activeProjects, totalProjects, recentProjects] =
        await Promise.all([
          ctx.db.member.count({
            where: {
              organizationId: input.organizationId,
              deletedAt: null,
            },
          }),
          ctx.db.project.count({
            where: {
              organizationId: input.organizationId,
              status: PROJECT_STATUS.EDITING,
              deletedAt: null,
            },
          }),
          ctx.db.project.count({
            where: {
              organizationId: input.organizationId,
              status: {
                not: PROJECT_STATUS.REJECTED,
              },
              deletedAt: null,
            },
          }),
          ctx.db.project.findMany({
            where: {
              deletedAt: null,
              organizationId: input.organizationId,
              updatedAt: { gt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
              // Exclude rejected projects
              status: {
                not: PROJECT_STATUS.REJECTED,
              },
              // Only show recent projects created by the user if not an admin
              ...(!isAdmin && {
                creatorId: ctx.session.user.id,
              }),
            },
            select: {
              id: true,
              name: true,
              status: true,
              updatedAt: true,
              startDate: true,
              endDate: true,
              _count: {
                select: {
                  slides: {
                    where: { deletedAt: null },
                  },
                },
              },
              locations: {
                where: { deletedAt: null },
                select: {
                  id: true,
                  shortId: true,
                  name: true,
                  city: true,
                  state: true,
                },
              },
              sublocations: {
                where: { deletedAt: null },
                select: {
                  id: true,
                  shortId: true,
                  name: true,
                  locationId: true,
                },
              },
            },
            take: 4,
            orderBy: { createdAt: "desc" },
          }),
        ]);

      return {
        teamMembers,
        activeProjects,
        totalProjects,
        recentProjects: computeProjectsStatus(recentProjects),
      };
    }),
  updateById: protectedProcedure
    .input(z.object({ id: z.string() }).merge(ProjectUpdateSchema))
    .mutation(async ({ ctx, input }) => {
      const updatedProject = await ctx.db.project.update({
        where: { id: input.id },
        data: { ...omit(input, ["id"]) },
      });

      // Return project with computed status
      return computeProjectStatus(updatedProject);
    }),
  deleteById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.project.update({
        where: { id: input.id },
        data: { deletedAt: new Date() },
      });
    }),
  publishProject: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        locations: z.array(z.string()),
        sublocations: z.array(z.string()),
        startDate: z.string(),
        endDate: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Fetch project with org metadata for permission evaluation
      const project = await ctx.db.project.findUnique({
        where: { id: input.id, deletedAt: null },
        include: {
          organization: { select: { id: true, metadata: true } },
        },
      });

      if (!project) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Project not found",
        });
      }

      // Check if user has permission to publish the project
      await requirePermission({
        ctx,
        organizationId: project.organizationId,
        spec: { project: ["publish"] },
      });

      const updatedProject = await ctx.db.project.update({
        where: { id: input.id },
        data: {
          startDate: new Date(input.startDate),
          endDate: new Date(input.endDate),
          status: PROJECT_STATUS.APPROVED,
          locations: { set: input.locations.map((id) => ({ id })) },
          sublocations: { set: input.sublocations.map((id) => ({ id })) },
        },
      });

      // Return project with computed status
      return computeProjectStatus(updatedProject);
    }),
  resubmitForApproval: protectedProcedure
    .input(ProjectResubmissionSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      // Check if project exists and is rejected
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId, deletedAt: null },
        include: {
          organization: true,
        },
      });

      if (!project) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Project not found",
        });
      }

      if (project.status !== PROJECT_STATUS.REJECTED) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Only rejected projects can be resubmitted for approval",
        });
      }

      // Check if user is the project creator or admin
      const isCreator = project.creatorId === userId;
      const userMembership = await ctx.db.member.findFirst({
        where: {
          userId,
          organizationId: project.organizationId,
          deletedAt: null,
        },
      });

      if (!isCreator && (!userMembership || userMembership.role !== "admin")) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to resubmit this project",
        });
      }

      // Check if there's already a pending approval
      const existingApproval = await ctx.db.projectApproval.findFirst({
        where: {
          projectId: input.projectId,
          status: "pending",
          deletedAt: null,
        },
      });

      if (existingApproval) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Project already has a pending approval request",
        });
      }

      // Update project details if provided
      const updateData: any = {
        status: PROJECT_STATUS.EDITING, // Reset to editing so user can make changes if needed
      };

      if (input.name) {
        updateData.name = input.name;
      }
      if (input.startDate) {
        updateData.startDate = new Date(input.startDate);
      }
      if (input.endDate) {
        updateData.endDate = new Date(input.endDate);
      }

      const updatedProject = await ctx.db.project.update({
        where: { id: input.projectId },
        data: updateData,
      });

      // Update project locations if provided
      if (input.locations && input.locations.length > 0) {
        await ctx.db.project.update({
          where: { id: input.projectId },
          data: {
            locations: {
              set: input.locations.map((locationId) => ({ id: locationId })),
            },
          },
        });
      }

      // Update project sublocations if provided
      if (input.sublocations && input.sublocations.length > 0) {
        await ctx.db.project.update({
          where: { id: input.projectId },
          data: {
            sublocations: {
              set: input.sublocations.map((sublocationId) => ({
                id: sublocationId,
              })),
            },
          },
        });
      }

      return computeProjectStatus(updatedProject);
    }),
  getActivityStats: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { todayStart, todayEnd } = getTodayStartAndEnd();

      // Calculate date ranges for new metrics
      const now = new Date();
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const sevenDaysFromNow = new Date(
        now.getTime() + 7 * 24 * 60 * 60 * 1000,
      );

      const [
        activeProjectsCount,
        approvedProjectsCount,
        editingProjectsCount,
        completedProjectsCount,
        totalSlides,
        activeProjects,
        totalLocations,
        totalSublocations,
        newDeploymentsThisMonth,
        deploymentsEndingSoon,
      ] = await Promise.all([
        // Count approved projects that are currently active (within date range)
        ctx.db.project.count({
          where: {
            deletedAt: null,
            organizationId: input.organizationId,
            status: PROJECT_STATUS.APPROVED,
            startDate: { lte: todayEnd },
            endDate: { gte: todayStart },
          },
        }),
        // Count approved projects that are not completed and not active
        ctx.db.project.count({
          where: {
            deletedAt: null,
            organizationId: input.organizationId,
            status: PROJECT_STATUS.APPROVED,
            OR: [
              // Projects with no start date (not active, not completed)
              { startDate: null },
              // Projects not yet started (future projects)
              { startDate: { gt: todayEnd } },
            ],
            // Exclude completed projects (past their end date)
            AND: [
              {
                OR: [
                  // Projects with no end date
                  { endDate: null },
                  // Projects not yet ended
                  { endDate: { gte: todayStart } },
                ],
              },
            ],
          },
        }),
        // Count projects that are currently being edited
        ctx.db.project.count({
          where: {
            deletedAt: null,
            organizationId: input.organizationId,
            status: PROJECT_STATUS.EDITING,
          },
        }),
        // Count approved projects that are completed (past their end date)
        ctx.db.project.count({
          where: {
            deletedAt: null,
            organizationId: input.organizationId,
            status: PROJECT_STATUS.APPROVED,
            endDate: { lt: todayStart },
          },
        }),
        // Count total slides across all projects
        ctx.db.slide.count({
          where: {
            project: {
              deletedAt: null,
              organizationId: input.organizationId,
              status: PROJECT_STATUS.APPROVED,
              startDate: { lte: todayEnd },
              endDate: { gte: todayStart },
            },
          },
        }),
        // Get active projects (approved + within date range)
        ctx.db.project.findMany({
          where: {
            deletedAt: null,
            organizationId: input.organizationId,
            status: PROJECT_STATUS.APPROVED,
            startDate: { lte: todayEnd },
            endDate: { gte: todayStart },
          },
          include: {
            locations: {
              where: { deletedAt: null },
              select: { id: true },
            },
            sublocations: {
              where: { deletedAt: null },
              select: { id: true },
            },
          },
        }),
        // Count total locations in the organization
        ctx.db.location.count({
          where: {
            organizationId: input.organizationId,
          },
        }),
        // Count total sublocations in the organization
        ctx.db.subLocation.count({
          where: {
            location: {
              organizationId: input.organizationId,
            },
          },
        }),
        // Count new deployments this month (approved projects with locations/sublocations that started this month)
        ctx.db.project.count({
          where: {
            organizationId: input.organizationId,
            status: PROJECT_STATUS.APPROVED,
            startDate: { gte: monthStart },
            OR: [{ locations: { some: {} } }, { sublocations: { some: {} } }],
          },
        }),
        // Count deployments ending soon (approved projects with locations/sublocations ending in next 7 days)
        ctx.db.project.count({
          where: {
            organizationId: input.organizationId,
            status: PROJECT_STATUS.APPROVED,
            endDate: {
              gte: todayStart,
              lte: sevenDaysFromNow,
            },
            OR: [{ locations: { some: {} } }, { sublocations: { some: {} } }],
          },
        }),
      ]);

      // Calculate unique locations and sublocations from active/approved projects
      const uniqueLocationIds = new Set<string>();
      const uniqueSublocationIds = new Set<string>();
      let totalLocationDeployments = 0;

      activeProjects.forEach((project) => {
        project.locations.forEach((location) => {
          uniqueLocationIds.add(location.id);
          totalLocationDeployments++;
        });
        project.sublocations.forEach((sublocation) => {
          uniqueSublocationIds.add(sublocation.id);
        });
      });

      const activeLocationsCount = uniqueLocationIds.size;
      const activeSublocationsCount = uniqueSublocationIds.size;
      const projectsWithLocations = activeProjects.filter(
        (p) => p.locations.length > 0 || p.sublocations.length > 0,
      ).length;

      // Calculate utilization rates
      const locationUtilizationRate =
        totalLocations > 0
          ? Math.round((activeLocationsCount / totalLocations) * 100)
          : 0;

      const sublocationUtilizationRate =
        totalSublocations > 0
          ? Math.round((activeSublocationsCount / totalSublocations) * 100)
          : 0;

      // Calculate average deployments per project
      const totalActiveAndApproved =
        activeProjectsCount + approvedProjectsCount;
      const averageLocationsPerProject =
        totalActiveAndApproved > 0
          ? Math.round(
              (totalLocationDeployments / totalActiveAndApproved) * 10,
            ) / 10
          : 0;

      return {
        activeProjectsCount,
        approvedProjectsCount,
        editingProjectsCount,
        completedProjectsCount,
        totalSlides,
        activeLocationsCount,
        activeSublocationsCount,
        totalLocations,
        totalSublocations,
        totalLocationDeployments,
        projectsWithLocations,
        locationUtilizationRate,
        sublocationUtilizationRate,
        averageLocationsPerProject,
        deploymentEfficiency:
          projectsWithLocations > 0
            ? Math.round((projectsWithLocations / totalActiveAndApproved) * 100)
            : 0,
        newDeploymentsThisMonth,
        deploymentsEndingSoon,
      };
    }),
  getProjectActivityTimeline: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      const [recentProjectActivity, slidesGrowth] = await Promise.all([
        ctx.db.project.findMany({
          where: {
            organizationId: input.organizationId,
            updatedAt: { gte: thirtyDaysAgo },
            deletedAt: null,
            status: {
              not: PROJECT_STATUS.REJECTED,
            },
          },
          select: {
            id: true,
            name: true,
            status: true,
            updatedAt: true,
            createdAt: true,
            startDate: true,
            endDate: true,
            _count: {
              select: {
                slides: {
                  where: { deletedAt: null },
                },
                locations: {
                  where: { deletedAt: null },
                },
                sublocations: {
                  where: { deletedAt: null },
                },
              },
            },
            locations: {
              where: { deletedAt: null },
              select: {
                id: true,
                shortId: true,
                name: true,
                city: true,
                state: true,
              },
            },
            sublocations: {
              where: { deletedAt: null },
              select: {
                id: true,
                shortId: true,
                name: true,
                locationId: true,
              },
            },
          },
          orderBy: { updatedAt: "desc" },
          take: 10,
        }),
        ctx.db.slide.count({
          where: {
            project: {
              organizationId: input.organizationId,
              deletedAt: null,
              status: {
                not: PROJECT_STATUS.REJECTED,
              },
            },
            createdAt: { gte: thirtyDaysAgo },
          },
        }),
      ]);

      // Compute dynamic status for recent activity
      const processedRecentActivity = computeProjectsStatus(
        recentProjectActivity,
      );

      return {
        recentActivity: processedRecentActivity,
        slidesAddedThisMonth: slidesGrowth,
        activeDeployments: processedRecentActivity.filter(
          (p) =>
            p.status === PROJECT_STATUS.ACTIVE &&
            (p._count.locations > 0 || p._count.sublocations > 0),
        ).length,
      };
    }),
});
