import {
  LocationAssignmentAssignManySchema,
  LocationAssignmentGetForMemberSchema,
  LocationAssignmentGetMyAssignmentsSchema,
  LocationAssignmentRevokeSchema,
  LocationAssignmentSetForMemberSchema,
} from "@/schemas/location-assignment.schemas";
import { Roles } from "@/types/organization.types";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const locationAssignmentsRouter = createTRPCRouter({
  // Get assignments for a member (admin or the member themselves)
  getForMember: protectedProcedure
    .input(LocationAssignmentGetForMemberSchema)
    .query(async ({ ctx, input }) => {
      const orgMember = await ctx.db.member.findFirst({
        where: {
          organizationId: input.orgId,
          userId: ctx.session.user.id,
          deletedAt: null,
        },
      });

      if (!orgMember) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Not a member of organization",
        });
      }

      const isAdmin = orgMember.role === Roles.ADMIN;
      if (!isAdmin && orgMember.id !== input.memberId) {
        return [];
      }

      const assignments = await ctx.db.locationAssignment.findMany({
        where: { memberId: input.memberId, deletedAt: null },
        include: { location: true },
        orderBy: { createdAt: "asc" },
      });

      return assignments;
    }),

  // Get assignments for the current user
  getMyAssignments: protectedProcedure
    .input(LocationAssignmentGetMyAssignmentsSchema)
    .query(async ({ ctx, input }) => {
      const member = await ctx.db.member.findFirst({
        where: {
          organizationId: input.orgId,
          userId: ctx.session.user.id,
          deletedAt: null,
        },
      });

      if (!member) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Not a member of organization",
        });
      }

      const assignments = await ctx.db.locationAssignment.findMany({
        where: { memberId: member.id, deletedAt: null },
        include: { location: true },
        orderBy: { createdAt: "asc" },
      });

      return assignments;
    }),

  // Replace the entire set of assignments for a member (admin-only)
  setForMember: protectedProcedure
    .input(LocationAssignmentSetForMemberSchema)
    .mutation(async ({ ctx, input }) => {
      // verify caller is admin
      const callerMember = await ctx.db.member.findFirst({
        where: {
          organizationId: input.orgId,
          userId: ctx.session.user.id,
          deletedAt: null,
        },
      });

      if (!callerMember || callerMember.role !== Roles.ADMIN) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Admin role required",
        });
      }

      // validate target member
      const targetMember = await ctx.db.member.findUnique({
        where: { id: input.memberId },
      });
      if (!targetMember || targetMember.organizationId !== input.orgId) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Member not found in organization",
        });
      }

      // validate locations belong to org
      const validLocations = await ctx.db.location.findMany({
        where: {
          id: { in: input.locationIds },
          organizationId: input.orgId,
          deletedAt: null,
        },
        select: { id: true },
      });

      if (validLocations.length !== input.locationIds.length) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "One or more locations are invalid",
        });
      }

      // perform transaction: restore/create new ones, soft-delete removed ones
      await ctx.db.$transaction(async (tx) => {
        const existing = await tx.locationAssignment.findMany({
          where: { memberId: input.memberId },
        });
        const existingMap = new Map(existing.map((e) => [e.locationId, e]));

        // restore or create desired assignments
        for (const locId of input.locationIds) {
          const found = existingMap.get(locId);
          if (found) {
            if (found.deletedAt) {
              await tx.locationAssignment.update({
                where: { id: found.id },
                data: { deletedAt: null },
              });
            }
          } else {
            await tx.locationAssignment.create({
              data: {
                organizationId: input.orgId,
                memberId: input.memberId,
                locationId: locId,
              },
            });
          }
        }

        // soft-delete assignments not in the new set
        const toRemove = existing.filter(
          (e) => !input.locationIds.includes(e.locationId) && !e.deletedAt,
        );
        if (toRemove.length > 0) {
          await tx.locationAssignment.updateMany({
            where: { id: { in: toRemove.map((r) => r.id) } },
            data: { deletedAt: new Date() },
          });
        }
      });

      return await ctx.db.locationAssignment.findMany({
        where: { memberId: input.memberId, deletedAt: null },
        include: { location: true },
      });
    }),

  // Assign multiple locations to a member (admin-only, idempotent)
  assignMany: protectedProcedure
    .input(
      z.object({
        orgId: z.string(),
        memberId: z.string(),
        locationIds: z.array(z.string()),
      }),
    )
    .input(LocationAssignmentAssignManySchema)
    .mutation(async ({ ctx, input }) => {
      const callerMember = await ctx.db.member.findFirst({
        where: {
          organizationId: input.orgId,
          userId: ctx.session.user.id,
          deletedAt: null,
        },
      });

      if (!callerMember || callerMember.role !== Roles.ADMIN) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Admin role required",
        });
      }

      const targetMember = await ctx.db.member.findUnique({
        where: { id: input.memberId },
      });
      if (!targetMember || targetMember.organizationId !== input.orgId) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Member not found in organization",
        });
      }

      const validLocations = await ctx.db.location.findMany({
        where: {
          id: { in: input.locationIds },
          organizationId: input.orgId,
          deletedAt: null,
        },
        select: { id: true },
      });

      if (validLocations.length === 0) return [];

      const existing = await ctx.db.locationAssignment.findMany({
        where: { memberId: input.memberId },
      });
      const existingMap = new Map(existing.map((e) => [e.locationId, e]));

      const createdOrRestored: any[] = [];

      await ctx.db.$transaction(async (tx) => {
        for (const loc of validLocations) {
          const found = existingMap.get(loc.id);
          if (found) {
            if (found.deletedAt) {
              const updated = await tx.locationAssignment.update({
                where: { id: found.id },
                data: { deletedAt: null },
              });
              createdOrRestored.push(updated);
            }
          } else {
            const created = await tx.locationAssignment.create({
              data: {
                organizationId: input.orgId,
                memberId: input.memberId,
                locationId: loc.id,
              },
            });
            createdOrRestored.push(created);
          }
        }
      });

      return await ctx.db.locationAssignment.findMany({
        where: { memberId: input.memberId, deletedAt: null },
        include: { location: true },
      });
    }),

  // Revoke a single assignment (admin-only)
  revoke: protectedProcedure
    .input(
      z.object({
        orgId: z.string(),
        memberId: z.string(),
        locationId: z.string(),
      }),
    )
    .input(LocationAssignmentRevokeSchema)
    .mutation(async ({ ctx, input }) => {
      const callerMember = await ctx.db.member.findFirst({
        where: {
          organizationId: input.orgId,
          userId: ctx.session.user.id,
          deletedAt: null,
        },
      });
      if (!callerMember || callerMember.role !== Roles.ADMIN) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Admin role required",
        });
      }

      const assignment = await ctx.db.locationAssignment.findFirst({
        where: { memberId: input.memberId, locationId: input.locationId },
      });
      if (!assignment || assignment.deletedAt) {
        // nothing to do
        return { success: true };
      }

      await ctx.db.locationAssignment.update({
        where: { id: assignment.id },
        data: { deletedAt: new Date() },
      });
      return { success: true };
    }),

  // Get all members with their location assignment count (admin-only)
  getMembersWithLocationCount: protectedProcedure
    .input(z.object({ orgId: z.string() }))
    .query(async ({ ctx, input }) => {
      const members = await ctx.db.member.findMany({
        where: {
          organizationId: input.orgId,
          deletedAt: null,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          _count: {
            select: {
              locationAssignments: {
                where: { deletedAt: null },
              },
            },
          },
        },
      });

      return members.map((m) => ({
        id: m.id,
        name: m.user.name,
        email: m.user.email,
        image: m.user.image,
        role: m.role,
        assignmentCount: m._count.locationAssignments,
      }));
    }),

  // Get members assigned to a location (admin-only)
  getAssignedMembersForLocation: protectedProcedure
    .input(z.object({ orgId: z.string(), locationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const callerMember = await ctx.db.member.findFirst({
        where: {
          organizationId: input.orgId,
          userId: ctx.session.user.id,
          deletedAt: null,
        },
      });
      if (!callerMember || callerMember.role !== Roles.ADMIN) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Admin role required",
        });
      }

      const assignments = await ctx.db.locationAssignment.findMany({
        where: { locationId: input.locationId, deletedAt: null },
        include: { member: { include: { user: true } } },
        orderBy: { createdAt: "asc" },
      });

      return assignments.map((a) => ({
        id: a.member.id,
        role: a.member.role,
        user: a.member.user,
      }));
    }),
});
