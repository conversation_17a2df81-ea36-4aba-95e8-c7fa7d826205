import { PROJECT_STATUS } from "@/libs/constants";
import { filterSchema } from "@/schemas/api.schemas";
import {
  SlideCreateManySchema,
  SlideCreateSchema,
  SlideUpdateSchema,
} from "@/schemas/slide.schemas";
import { getTodayStartAndEnd } from "@/utils/dates";
import { omit } from "@/utils/omit";
import { TRPCError } from "@trpc/server";
import { generateKeyBetween } from "fractional-indexing";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "../trpc";

export const slidesRouter = createTRPCRouter({
  create: protectedProcedure
    .input(SlideCreateSchema)
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.slide.create({
        data: input,
      });
    }),
  createMany: protectedProcedure
    .input(SlideCreateManySchema)
    .mutation(async ({ ctx, input }) => {
      // Get ALL existing slides for this project ordered by their current order
      const existingSlides = await ctx.db.slide.findMany({
        where: { projectId: input.projectId, deletedAt: null },
        select: { order: true },
        orderBy: { order: "asc" },
      });

      // Determine reference point - either after the specified slide or at the end
      let referenceOrder: string | null = null;

      if (input.lastSlideId) {
        const lastSlide = await ctx.db.slide.findUnique({
          where: { id: input.lastSlideId, deletedAt: null },
          select: { order: true },
        });
        referenceOrder = lastSlide?.order || null;
      } else if (existingSlides.length > 0) {
        // If no specific reference, add after the last slide (or null if no slides)
        referenceOrder =
          existingSlides[existingSlides.length - 1]?.order || null;
      }

      // Prepare bulk data with unique fractional order values
      const slidesData = [];
      let currentOrder = referenceOrder;

      for (const { imageUrl, objectKey } of input.images) {
        // Generate next order value after the current one (or null if it's the first slide)
        const nextOrder = generateKeyBetween(currentOrder, null);

        slidesData.push({
          projectId: input.projectId,
          imageUrl,
          objectKey,
          order: nextOrder,
        });

        // Update current order for next iteration (or null if it's the last slide)
        currentOrder = nextOrder;
      }

      // Batch insert all slides at once
      return await ctx.db.slide.createMany({
        data: slidesData,
      });
    }),
  getAll: protectedProcedure
    .input(z.object({ projectId: z.string(), ...filterSchema }))
    .query(async ({ ctx, input }) => {
      const take = input?.take || 500;

      const data = await ctx.db.slide.findMany({
        where: {
          projectId: input.projectId,
          deletedAt: null,
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { order: "asc" },
      });

      const sortedSlides = data.sort((a, b) =>
        a.order < b.order ? -1 : a.order > b.order ? 1 : 0,
      );

      const result = { data: sortedSlides, cursor: "" };

      if (sortedSlides.length < take) return result;

      return { ...result, cursor: sortedSlides.at(-1)?.id || "" };
    }),
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const data = await ctx.db.slide.findUnique({
        where: { id: input.id, deletedAt: null },
      });

      if (!data) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Slide not found",
        });
      }

      return data;
    }),
  updateById: protectedProcedure
    .input(z.object({ id: z.string() }).merge(SlideUpdateSchema))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.slide.update({
        where: { id: input.id },
        data: { ...omit(input, ["id"]) },
      });
    }),
  batchUpdate: protectedProcedure
    .input(
      z.object({
        slides: z.array(z.object({ id: z.string(), order: z.string() })),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.$transaction(
        input.slides.map((slide) =>
          ctx.db.slide.update({
            where: { id: slide.id },
            data: { order: slide.order },
          }),
        ),
      );
    }),
  deleteById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.slide.update({
        where: { id: input.id },
        data: { deletedAt: new Date() },
      });
    }),
  getForLocation: publicProcedure
    .input(
      z.object({
        locationId: z.string(),
        subLocationId: z.string().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      if (!input.locationId) {
        return { slides: [], project: null };
      }

      const { todayStart, todayEnd } = getTodayStartAndEnd();

      // Get slides from active projects at the specified location
      const slides = await ctx.db.slide.findMany({
        where: {
          project: {
            status: PROJECT_STATUS.APPROVED, // Only approved projects
            startDate: { lte: todayEnd }, // Projects that have started
            endDate: { gte: todayStart }, // Projects that have not ended
            locations: { some: { shortId: input.locationId } },
            ...(input.subLocationId && {
              sublocations: { some: { shortId: input.subLocationId } },
            }),
            deletedAt: null,
          },
          deletedAt: null,
        },
        select: {
          id: true,
          imageUrl: true,
          order: true,
          duration: true,
          isImportant: true,
          projectId: true,
          project: {
            select: {
              slideDuration: true,
              createdAt: true, // Need this to sort projects by creation date
            },
          },
        },
        // Sort by project creation date
        orderBy: [{ project: { createdAt: "asc" } }],
        take: 30, // Limit to 30 slides
      });

      // Track when each project was created using a Map for efficient lookups
      const projectOrderMap = new Map<string, Date>();
      slides.forEach((slide) => {
        if (!projectOrderMap.has(slide.projectId)) {
          projectOrderMap.set(slide.projectId, slide.project.createdAt);
        }
      });

      // Sort slides: oldest projects first, then by slide order within each project
      const sortedSlides = slides
        .sort((a, b) => {
          // Sort by project creation date first
          if (a.projectId !== b.projectId) {
            const aProjectDate = projectOrderMap.get(a.projectId)!;
            const bProjectDate = projectOrderMap.get(b.projectId)!;
            return aProjectDate.getTime() - bProjectDate.getTime();
          }

          // Then sort by slide order within the same project (works for fractional indexing order)
          return a.order < b.order ? -1 : a.order > b.order ? 1 : 0;
        })
        // Return only the data we need
        .map((slide) => ({
          id: slide.id,
          imageUrl: slide.imageUrl,
          order: slide.order,
          duration: slide.duration,
          isImportant: slide.isImportant,
          projectId: slide.projectId,
        }));

      return { slides: sortedSlides, project: slides[0]?.project };
    }),
});
