import { env } from "@/env";
import {
  APPROVAL_STATUS,
  FILTER_TAKE,
  NOTIFICATION_TYPES,
  PROJECT_STATUS,
} from "@/libs/constants";
import {
  sendApprovedNotificationEmail,
  sendBatchApprovalNotificationEmails,
  sendRejectedNotificationEmail,
} from "@/libs/mail";
import { filterSchema } from "@/schemas/api.schemas";
import {
  ApprovalFilterSchema,
  ProjectApprovalRequestSchema,
  ProjectApprovalReviewSchema,
} from "@/schemas/approval.schemas";
import { requirePermission } from "@/server/permissions/require-permission";
import { formatDate } from "@/utils/format-date";
import { getAllApprovals } from "@prisma/client/sql";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const approvalsRouter = createTRPCRouter({
  // Submit project for approval
  submitForApproval: protectedProcedure
    .input(ProjectApprovalRequestSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      // Check if project exists and user has permission
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId, deletedAt: null },
        include: {
          organization: {
            include: {
              members: {
                where: { deletedAt: null },
                include: {
                  user: true,
                },
              },
            },
          },
          slides: {
            where: { deletedAt: null },
          },
          _count: {
            select: { slides: true },
          },
        },
      });

      // Get current user info
      const currentUser = await ctx.db.user.findUnique({
        where: { id: userId },
        select: { id: true, name: true, email: true },
      });

      if (!project) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Project not found",
        });
      }

      // Check if there's already a pending approval
      const existingApproval = await ctx.db.projectApproval.findFirst({
        where: {
          projectId: input.projectId,
          status: APPROVAL_STATUS.PENDING,
          deletedAt: null,
        },
      });

      if (existingApproval) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Project already has a pending approval request",
        });
      }

      // Server-side permission check: user must be able to submit projects for approval
      await requirePermission({
        ctx,
        organizationId: project.organizationId,
        spec: { project: ["submit"] },
      });

      // Create approval request
      const approval = await ctx.db.projectApproval.create({
        data: {
          projectId: input.projectId,
          requesterId: userId,
          comments: input.comments,
          status: APPROVAL_STATUS.PENDING,
        },
        include: {
          project: true,
          requester: true,
        },
      });

      // Update project with dates, locations, and status
      const updateData: any = {
        status: PROJECT_STATUS.PENDING_APPROVAL,
      };

      if (input.startDate) {
        updateData.startDate = new Date(input.startDate);
      }
      if (input.endDate) {
        updateData.endDate = new Date(input.endDate);
      }

      await ctx.db.project.update({
        where: { id: input.projectId },
        data: updateData,
      });

      // Update project locations if provided
      if (input.locations && input.locations.length > 0) {
        await ctx.db.project.update({
          where: { id: input.projectId },
          data: {
            locations: {
              set: input.locations.map((locationId) => ({ id: locationId })),
            },
          },
        });
      }

      // Update project sublocations if provided
      if (input.sublocations && input.sublocations.length > 0) {
        await ctx.db.project.update({
          where: { id: input.projectId },
          data: {
            sublocations: {
              set: input.sublocations.map((sublocationId) => ({
                id: sublocationId,
              })),
            },
          },
        });
      }

      // Create notifications for organization admins/approvers
      const admins = project.organization!.members.filter(
        (member) => member.role === "admin" && member.userId !== userId,
      );

      if (admins.length > 0) {
        await ctx.db.approvalNotification.createMany({
          data: admins.map((admin) => ({
            approvalId: approval.id,
            userId: admin.userId,
            type: NOTIFICATION_TYPES.APPROVAL_REQUESTED,
          })),
        });

        // Send batch email notifications to admins
        const baseUrl = env.NEXT_PUBLIC_APP_BASE_URL;
        const approvalLink = `${baseUrl}/${project.organization?.slug}/approvals`;

        const adminEmails = admins.map((admin) => admin.user.email);

        // Send all emails in a single batch request, but don't wait for completion
        sendBatchApprovalNotificationEmails({
          adminEmails,
          projectName: project.name,
          requesterName: currentUser?.name || "Unknown User",
          requesterEmail: currentUser?.email || "",
          organizationName: project.organization!.name,
          comments: input.comments,
          startDate: formatDate(input.startDate),
          endDate: formatDate(input.endDate),
          slideCount: project._count.slides,
          approvalLink,
        }).catch((error: Error) => {
          // Log email errors but don't fail the approval creation
          console.error("Failed to send batch approval emails:", error);
        });
      }

      return approval;
    }),

  // Review approval (approve/reject)
  reviewApproval: protectedProcedure
    .input(ProjectApprovalReviewSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      const approval = await ctx.db.projectApproval.findUnique({
        where: { id: input.approvalId, deletedAt: null },
        include: {
          project: {
            include: {
              organization: {
                include: {
                  members: {
                    where: { deletedAt: null },
                  },
                },
              },
              _count: {
                select: { slides: { where: { deletedAt: null } } },
              },
            },
          },
          requester: true,
        },
      });

      if (!approval) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Approval request not found",
        });
      }

      if (approval.status !== APPROVAL_STATUS.PENDING) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Approval request has already been reviewed",
        });
      }

      // Server-side permission check: user must have approval.review capability
      await requirePermission({
        ctx,
        organizationId: approval.project.organizationId,
        spec: { approval: ["review"] },
      });

      // Update approval
      const updatedApproval = await ctx.db.projectApproval.update({
        where: { id: input.approvalId },
        data: {
          status: input.status,
          approverId: userId,
          reviewedAt: new Date(),
          reviewComments: input.reviewComments,
        },
        include: {
          project: true,
          requester: true,
          approver: true,
        },
      });

      // Update project status based on approval decision
      const newProjectStatus =
        input.status === APPROVAL_STATUS.APPROVED
          ? PROJECT_STATUS.APPROVED
          : PROJECT_STATUS.REJECTED;

      await ctx.db.project.update({
        where: { id: approval.projectId },
        data: { status: newProjectStatus },
      });

      // Create notification for requester
      await ctx.db.approvalNotification.create({
        data: {
          approvalId: input.approvalId,
          userId: approval.requesterId,
          type:
            input.status === APPROVAL_STATUS.APPROVED
              ? NOTIFICATION_TYPES.APPROVED
              : NOTIFICATION_TYPES.REJECTED,
        },
      });

      // Send email notification to requester if approved or rejected
      const baseUrl = env.NEXT_PUBLIC_APP_BASE_URL;
      const projectLink = `${baseUrl}/${approval.project.organization?.slug}/projects/${approval.projectId}`;

      if (input.status === APPROVAL_STATUS.APPROVED) {
        sendApprovedNotificationEmail({
          requesterEmail: approval.requester.email,
          projectName: approval.project.name,
          requesterName: approval.requester.name || "User",
          approverName: updatedApproval.approver?.name || "Administrator",
          organizationName:
            approval.project.organization?.name || "Organization",
          reviewComments: input.reviewComments,
          startDate: formatDate(approval.project.startDate),
          endDate: formatDate(approval.project.endDate),
          slideCount: approval.project._count.slides,
          projectLink,
        }).catch((error: Error) => {
          // Log email errors but don't fail the approval process
          console.error("Failed to send approved notification email:", error);
        });
      } else {
        // Rejected
        sendRejectedNotificationEmail({
          requesterEmail: approval.requester.email,
          projectName: approval.project.name,
          requesterName: approval.requester.name || "User",
          approverName: updatedApproval.approver?.name || "Administrator",
          organizationName:
            approval.project.organization?.name || "Organization",
          reviewComments: input.reviewComments,
          startDate: formatDate(approval.project.startDate),
          endDate: formatDate(approval.project.endDate),
          slideCount: approval.project._count.slides,
          projectLink,
        }).catch((error: Error) => {
          // Log email errors but don't fail the approval process
          console.error("Failed to send rejected notification email:", error);
        });
      }

      return updatedApproval;
    }),

  // Get all approvals for organization
  getAll: protectedProcedure
    .input(
      z
        .object({
          organizationId: z.string(),
          myRequests: z.boolean().optional(),
          ...filterSchema,
        })
        .merge(ApprovalFilterSchema),
    )
    .query(async ({ ctx, input }) => {
      const take = input?.take || FILTER_TAKE;
      const userId = ctx.session.user.id;

      const searchString = (input?.searchString || null) as string; // Doing this to get around type issues
      const status = (
        input.status === "all" ? null : input.status || null
      ) as string; // Doing this to get around type issues

      const projectId = (input.projectId || null) as string; // Doing this to get around type issues
      const requesterId = (input.myRequests ? userId : null) as string; // Doing this to get around type issues
      const cursor = (input.cursor || null) as string; // Doing this to get around type issues

      // Raw Typed Query
      // Using getAllApprovals SQL function to fetch approvals with pagination and filtering
      // This function is defined in the Prisma SQL directory file
      // NOTE: Raw SQL function should already filter out deleted records, but if not, update SQL to add deletedAt = null for all relevant tables
      const data = await ctx.db.$queryRawTyped(
        getAllApprovals(
          input.organizationId as any, // Doing this to get around type issues
          searchString as any, // First param for the LIKE clause // Doing this to get around type issues
          searchString as any, // Second param for the LIKE clause // Doing this to get around type issues
          searchString as any, // Third param for the LIKE clause
          status as any, // First param for status comparison
          status as any, // Second param for status check
          status as any, // Third param for status comparison
          projectId as any,
          projectId as any, // Second param for projectId
          requesterId as any,
          requesterId as any, // Second param for requesterId
          cursor as any,
          cursor as any, // Second param for cursor comparison
          take as any,
        ),
      );

      // Transform the data to match your expected format
      const transformedData = data.map((row) => ({
        id: row.id,
        projectId: row.projectId,
        requesterId: row.requesterId,
        approverId: row.approverId,
        status: row.status,
        submittedAt: row.submittedAt,
        reviewedAt: row.reviewedAt,
        comments: row.comments,
        reviewComments: row.reviewComments,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt,
        project: {
          id: row.project_id,
          name: row.project_name,
          status: row.project_status,
          startDate: row.project_start_date,
          endDate: row.project_end_date,
          _count: { slides: Number(row.slide_count) },
        },
        requester: {
          id: row.requester_id,
          name: row.requester_name,
          email: row.requester_email,
          image: row.requester_image,
        },
        approver: row.approver_id
          ? {
              id: row.approver_id,
              name: row.approver_name,
              email: row.approver_email,
              image: row.approver_image,
            }
          : null,
      }));

      const result = { data: transformedData, cursor: "" };
      if (transformedData.length < take) return result;
      return { ...result, cursor: transformedData.at(-1)?.id || "" };
    }),

  // Get pending approvals that need user's review
  getPendingReviews: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      // Check if user is admin in the organization
      const membership = await ctx.db.member.findFirst({
        where: {
          userId,
          organizationId: input.organizationId,
          role: "admin",
          deletedAt: null,
        },
      });

      if (!membership) {
        return [];
      }

      return await ctx.db.projectApproval.findMany({
        where: {
          deletedAt: null,
          project: { organizationId: input.organizationId, deletedAt: null },
          status: APPROVAL_STATUS.PENDING,
        },
        include: {
          project: {
            select: {
              id: true,
              name: true,
              status: true,
              startDate: true,
              endDate: true,
              _count: { select: { slides: { where: { deletedAt: null } } } },
            },
          },
          requester: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      });
    }),

  // Get approval by ID
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.db.projectApproval.findUnique({
        where: { id: input.id, deletedAt: null },
        include: {
          project: {
            include: {
              _count: { select: { slides: { where: { deletedAt: null } } } },
              slides: {
                where: { deletedAt: null },
                take: 5,
                orderBy: { order: "asc" },
              },
            },
          },
          requester: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          approver: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
        },
      });
    }),

  // Get rejected projects that can be resubmitted
  getRejectedProjects: protectedProcedure
    .input(
      z.object({
        organizationId: z.string(),
        myProjects: z.boolean().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      // Check user permissions
      const userMembership = await ctx.db.member.findFirst({
        where: {
          userId,
          organizationId: input.organizationId,
          deletedAt: null,
        },
      });

      if (!userMembership) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have access to this organization",
        });
      }

      const isAdmin = userMembership.role === "admin";

      return await ctx.db.project.findMany({
        where: {
          deletedAt: null,
          organizationId: input.organizationId,
          status: PROJECT_STATUS.REJECTED,
          // If not admin and myProjects is true, only show user's own projects
          // If admin, show all or user's own based on myProjects flag
          ...(input.myProjects || !isAdmin ? { creatorId: userId } : {}),
        },
        include: {
          _count: {
            select: {
              slides: { where: { deletedAt: null } },
            },
          },
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          // Get the latest rejection approval
          approvals: {
            where: {
              status: APPROVAL_STATUS.REJECTED,
              deletedAt: null,
            },
            include: {
              approver: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
            orderBy: {
              reviewedAt: "desc",
            },
            take: 1,
          },
        },
        orderBy: { updatedAt: "desc" },
      });
    }),

  // Get rejection history for a project
  getRejectionHistory: protectedProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      // Check if project exists and user has permission to view it
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId, deletedAt: null },
        include: {
          organization: {
            include: {
              members: {
                where: { userId, deletedAt: null },
              },
            },
          },
        },
      });

      if (!project) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Project not found",
        });
      }

      if (!project.organization) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Organization not found",
        });
      }

      const userMembership = project.organization.members[0];
      const isCreator = project.creatorId === userId;
      const isAdmin = userMembership?.role === "admin";

      if (!isCreator && !isAdmin) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to view this project's history",
        });
      }

      return await ctx.db.projectApproval.findMany({
        where: {
          projectId: input.projectId,
          status: APPROVAL_STATUS.REJECTED,
          deletedAt: null,
        },
        include: {
          approver: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          requester: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
        },
        orderBy: { reviewedAt: "desc" },
      });
    }),
});
