import { approvalsRouter } from "@/server/api/routers/approvals.router";
import { groupsRouter } from "@/server/api/routers/groups.router";
import { locationAssignmentsRouter } from "@/server/api/routers/location-assignments.router";
import { locationsRouter } from "@/server/api/routers/locations.router";
import { organizationsRouter } from "@/server/api/routers/organization.router";
import { paymentsRouter } from "@/server/api/routers/payments.router";
import { projectsRouter } from "@/server/api/routers/projects.router";
import { slidesRouter } from "@/server/api/routers/slides.router";
import { storageRouter } from "@/server/api/routers/storage.router";
import { userRouter } from "@/server/api/routers/user.router";
import { createCallerFactory, createTRPCRouter } from "@/server/api/trpc";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  user: userRouter,
  storage: storageRouter,
  payments: paymentsRouter,
  projects: projectsRouter,
  slides: slidesRouter,
  organizations: organizationsRouter,
  locations: locationsRouter,
  groups: groupsRouter,
  locationAssignments: locationAssignmentsRouter,
  approvals: approvalsRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
