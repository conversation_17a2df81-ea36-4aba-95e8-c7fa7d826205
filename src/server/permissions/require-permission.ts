import { db } from "@/server/db";
import { TRPCError } from "@trpc/server";
import { hasPermission } from "./evaluate";

// Minimal context typing (avoid circular import) – relies on runtime shape provided in trpc context
type Ctx = {
  session: { user: { id: string } } | null;
  db: typeof db;
};

interface RequirePermissionArgs {
  ctx: Ctx;
  organizationId: string;
  permissions: Record<string, string[]>; // e.g. { project: ["publish"] }
}

/**
 * Simplified permission check using Better Auth's built-in hasPermission API.
 * This replaces the complex custom permission evaluation system.
 */
export async function requirePermission({
  ctx,
  organizationId,
  permissions,
}: RequirePermissionArgs) {
  if (!ctx.session?.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  // Check if user has the required permissions using Better Auth
  const hasRequiredPermissions = await hasPermission({
    permissions,
    organizationId,
  });

  if (!hasRequiredPermissions) {
    // Create a readable error message
    const permissionList = Object.entries(permissions)
      .map(([resource, actions]) =>
        actions.map((action) => `${resource}.${action}`).join(", "),
      )
      .join(", ");

    throw new TRPCError({
      code: "FORBIDDEN",
      message: `Missing required permissions: ${permissionList}`,
    });
  }

  return true;
}

export default requirePermission;
