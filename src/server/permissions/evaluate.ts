import { auth } from "@/libs/auth";
import {
  sanitizeOverrides,
  type ResourceActions,
} from "@/libs/auth-permissions";
import { headers } from "next/headers";

/**
 * Simplified permission evaluation using Better Auth's built-in methods.
 * This function checks if a user has specific permissions using Better Auth's hasPermission API.
 */
export async function hasPermission(params: {
  permissions: Record<string, string[]>;
  organizationId?: string;
}): Promise<boolean> {
  try {
    const result = await auth.api.hasPermission({
      headers: await headers(),
      body: {
        permissions: params.permissions,
        organizationId: params.organizationId,
      },
    });
    return result.success;
  } catch (error) {
    console.error("Permission check failed:", error);
    return false;
  }
}

/**
 * Get role permissions from Better Auth for a specific role and organization.
 * This is used for dynamic roles that are stored in Better Auth.
 */
export async function getRolePermissions(params: {
  organizationId: string;
  roleName: string;
}): Promise<ResourceActions> {
  const { organizationId, roleName } = params;

  try {
    const role = await auth.api.getOrgRole({
      query: { organizationId, roleName },
      headers: await headers(),
    });
    return sanitizeOverrides(role.permission);
  } catch (error) {
    console.error("Failed to fetch role from Better Auth:", error);
    return {};
  }
}
