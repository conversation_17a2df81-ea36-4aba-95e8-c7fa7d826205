import { SettingsAssignmentsView } from "@/components/settings/organization/settings-assignments-view";
import { APP_NAME } from "@/libs/constants";

export const metadata = {
  title: `Location Assignments - ${APP_NAME}`,
};

interface Props {
  params: Promise<{ slug: string }>;
}

export default async function Page({ params }: Props) {
  const { slug } = await params;
  return <SettingsAssignmentsView slug={slug} />;
}
