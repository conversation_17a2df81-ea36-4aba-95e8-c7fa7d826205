import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import { authClient } from "@/libs/auth-client";
import { getStatements, type CapabilitySpec } from "@/libs/auth-permissions";
import {
  useOrganizationBySlug,
  useOrganizationMemberRole,
} from "@/queries/organization.queries";
import { useCallback, useMemo } from "react";

/**
 * usePermissions
 * ---------------------------------------------------------------------------
 * Simplified client‑side permission hook using Better Auth's built-in methods.
 *
 * Responsibilities:
 * 1. Reads the current organization (via slug) and member role.
 * 2. Uses Better Auth's hasPermission API for permission checks.
 * 3. Exposes a flexible `can()` helper for runtime checks.
 * 4. Provides `capability()` helper to construct specs programmatically.
 * 5. Supplies backward‑compatible boolean flags (existing UI still relies on these).
 *
 * Return Shape (partial):
 *   {
 *     role: "admin" | "manager" | "member" | undefined,
 *     can: (resource, ...actions) => boolean OR (specObject) => boolean, // Deprecated sync version
 *     canAsync: (resource, ...actions) => Promise<boolean> OR (specObject) => Promise<boolean>,
 *     capability: (resource, ...actions) => CapabilitySpec,
 *     statements: object, // Permission vocabulary
 *     // legacy flags:
 *     canPublishProject: boolean,
 *     canEditProject: boolean,
 *     ...
 *   }
 *
 * Usage Examples:
 * ---------------------------------------------------------------------------
 *   const perms = usePermissions();
 *
 *   // Preferred async approach (accurate permissions):
 *   if (await perms.canAsync("project", "publish")) {
 *     // show Publish button
 *   }
 *
 *   // Backward compatible sync approach (role-based approximation):
 *   if (perms.can("project", "publish")) {
 *     // show Publish button (less accurate)
 *   }
 *
 *   // Multiple required actions for one resource:
 *   if (await perms.canAsync({ project: ["update", "delete"] })) {
 *     // can edit AND delete
 *   }
 *
 *   // Building a spec separately:
 *   const needPublish = perms.capability("project", "publish");
 *   if (await perms.canAsync(needPublish)) {
 *     // publish ok
 *   }
 *
 * Migration Guidance:
 * - Prefer `canAsync()` over `can()` for accurate permission checking
 * - Use `can()` only for backward compatibility where sync behavior is required
 * - Legacy booleans will be removed in a later phase; avoid adding new code paths using them.
 *
 * Performance Notes:
 * - `canAsync()` uses Better Auth's API which may involve server calls
 * - `can()` uses local role-based logic for immediate results
 * - Consider caching results for frequently checked permissions
 *
 * Error Handling / Loading:
 * - While role/org are loading, role is undefined and `can()` returns false.
 * - Callers that need a loading state should pair with the underlying queries if necessary.
 */
export const usePermissions = () => {
  const slug = useOrganizationSlug();
  const orgQuery = useOrganizationBySlug(slug);
  const role = useOrganizationMemberRole();

  // Get the permission statements for building capability specs
  const statements = useMemo(() => getStatements(), []);

  // Create a capability builder function
  const capability = useCallback(
    (resource: string, ...actions: string[]): CapabilitySpec => {
      return { [resource]: actions.length ? actions : ["read"] };
    },
    [],
  );

  /**
   * Async runtime permission predicate using Better Auth's hasPermission API.
   * This is the preferred method for new code.
   */
  const canAsync = useCallback(
    async (arg1: any, ...rest: string[]): Promise<boolean> => {
      if (!arg1 || !orgQuery.data?.id) return false;

      let spec: CapabilitySpec;
      if (typeof arg1 === "string") {
        spec = { [arg1]: rest.length ? rest : ["read"] };
      } else {
        spec = arg1;
      }

      try {
        const result = await authClient.organization.hasPermission({
          permissions: spec,
          organizationId: orgQuery.data.id,
        });
        return result.data?.success || false;
      } catch (error) {
        console.error("Permission check failed:", error);
        return false;
      }
    },
    [orgQuery.data?.id],
  );

  /**
   * Synchronous permission check for backward compatibility.
   * Uses simplified role-based logic. For accurate permission checking, use canAsync.
   *
   * @deprecated Use canAsync for accurate permission checking
   */
  const can = useCallback(
    (arg1: any, ...rest: string[]): boolean => {
      if (!arg1 || !role) return false;

      let spec: CapabilitySpec;
      if (typeof arg1 === "string") {
        spec = { [arg1]: rest.length ? rest : ["read"] };
      } else {
        spec = arg1;
      }

      // Simple role-based permission checking for backward compatibility
      const isAdmin = role === "admin";
      const isManager = role === "manager" || isAdmin;
      const isMember = role === "member" || isManager;

      // Check each resource and action in the spec
      for (const [resource, actions] of Object.entries(spec)) {
        for (const action of actions) {
          // Define basic permission rules based on role
          switch (resource) {
            case "project":
              if (action === "publish" && !isAdmin) return false;
              if (action === "approve" && !isAdmin) return false;
              if (
                ["create", "read", "update", "submit"].includes(action) &&
                !isMember
              )
                return false;
              if (action === "delete" && !isAdmin) return false;
              break;
            case "approval":
              if (action === "review" && !isManager) return false;
              break;
            case "location":
              if (action === "manage" && !isAdmin) return false;
              if (["read", "assign"].includes(action) && !isManager)
                return false;
              break;
            case "organization":
              if (["update", "delete"].includes(action) && !isAdmin)
                return false;
              if (action === "read" && !isMember) return false;
              break;
            case "member":
              if (["create", "update", "delete"].includes(action) && !isAdmin)
                return false;
              if (action === "read" && !isMember) return false;
              break;
            default:
              // For unknown resources, only allow if admin
              if (!isAdmin) return false;
          }
        }
      }

      return true;
    },
    [role],
  );

  // Legacy flags - simplified implementation based on role
  // These provide backward compatibility but should be migrated to use can()
  const legacy = useMemo(() => {
    if (!role) {
      return {
        canPublishProject: false,
        canEditProject: false,
        canDeleteProject: false,
        canCreateProject: false,
        canManageMembers: false,
        canManageOrganization: false,
        canReviewApprovals: false,
        canManageLocations: false,
      };
    }

    // Simple role-based flags for backward compatibility
    const isAdmin = role === "admin";
    const isManager = role === "manager" || isAdmin;
    const isMember = role === "member" || isManager;

    return {
      canPublishProject: isAdmin,
      canEditProject: isMember,
      canDeleteProject: isAdmin,
      canCreateProject: isMember,
      canManageMembers: isAdmin,
      canManageOrganization: isAdmin,
      canReviewApprovals: isManager,
      canManageLocations: isAdmin,
    };
  }, [role]);

  return {
    role,
    can, // Deprecated: synchronous permission check for backward compatibility
    canAsync, // Preferred: async permission check using Better Auth API
    capability,
    statements,
    // Backward compatibility booleans
    ...legacy,
  };
};

export type UsePermissionsReturn = ReturnType<typeof usePermissions>;
