import ApprovalNotificationEmailTemplate from "@/emails/templates/approval-notification-email-template";
import ApprovedNotificationEmailTemplate from "@/emails/templates/approved-notification-email-template";
import ChangeEmailTemplate from "@/emails/templates/change-email-template";
import InvitationEmailTemplate from "@/emails/templates/invitation-email-template";
import PasswordResetEmailTemplate from "@/emails/templates/password-reset-email-template";
import RejectedNotificationEmailTemplate from "@/emails/templates/rejected-notification-email-template";
import VerifyEmailTemplate from "@/emails/templates/verify-email-template";
import { env } from "@/env";
import { APP_NAME } from "@/libs/constants";
import { render } from "@react-email/components";
import { Resend } from "resend";

export const resend = new Resend(env.RESEND_API_KEY);

const emailFrom = `${APP_NAME} <<EMAIL>>`;

export async function sendPasswordResetEmail(email: string, link: string) {
  const emailTemplate = PasswordResetEmailTemplate({ email, link });

  const text = await render(emailTemplate, { plainText: true });

  return await resend.emails.send({
    subject: "Reset your password",
    from: emailFrom,
    to: email,
    text,
    react: emailTemplate,
  });
}

export async function sendVerifyEmail(email: string, link: string) {
  const emailTemplate = VerifyEmailTemplate({ email, link });

  const text = await render(emailTemplate, { plainText: true });

  return await resend.emails.send({
    subject: "Verify your email address",
    from: emailFrom,
    to: email,
    text,
    react: emailTemplate,
  });
}

export async function sendEmailChangeEmail(
  email: string,
  newEmail: string,
  link: string,
) {
  const emailTemplate = ChangeEmailTemplate({ email: newEmail, link });

  const text = await render(emailTemplate, { plainText: true });

  return await resend.emails.send({
    subject: "Change your email address",
    from: emailFrom,
    to: email,
    text,
    react: emailTemplate,
  });
}

type InviteEmailParams = {
  inviteeEmail: string;
  invitationLink: string;
  organizationName: string;
  inviterName: string;
  inviterEmail: string;
};

export async function sendOrganizationInvitationEmail({
  inviteeEmail,
  invitationLink,
  organizationName,
  inviterName,
  inviterEmail,
}: InviteEmailParams) {
  const emailTemplate = InvitationEmailTemplate({
    organizationName,
    inviterName,
    inviteeEmail,
    invitationLink,
  });

  const text = await render(emailTemplate, { plainText: true });

  return await resend.emails.send({
    subject: `You've been invited to join the ${organizationName} organization on ${APP_NAME}`,
    from: emailFrom,
    to: inviteeEmail,
    text,
    replyTo: inviterEmail,
    react: emailTemplate,
  });
}

type ApprovalNotificationEmailParams = {
  adminEmail: string;
  projectName: string;
  requesterName: string;
  requesterEmail: string;
  organizationName: string;
  comments?: string;
  startDate?: string;
  endDate?: string;
  slideCount?: number;
  approvalLink: string;
};

export async function sendApprovalNotificationEmail({
  adminEmail,
  projectName,
  requesterName,
  requesterEmail,
  organizationName,
  comments,
  startDate,
  endDate,
  slideCount,
  approvalLink,
}: ApprovalNotificationEmailParams) {
  const emailTemplate = ApprovalNotificationEmailTemplate({
    projectName,
    requesterName,
    requesterEmail,
    organizationName,
    comments,
    startDate,
    endDate,
    slideCount,
    approvalLink,
  });

  const text = await render(emailTemplate, { plainText: true });

  return await resend.emails.send({
    subject: `New project approval request: ${projectName}`,
    from: emailFrom,
    to: adminEmail,
    text,
    replyTo: requesterEmail,
    react: emailTemplate,
  });
}

type BatchApprovalNotificationParams = {
  adminEmails: string[];
  projectName: string;
  requesterName: string;
  requesterEmail: string;
  organizationName: string;
  comments?: string;
  startDate?: string;
  endDate?: string;
  slideCount?: number;
  approvalLink: string;
};

export async function sendBatchApprovalNotificationEmails({
  adminEmails,
  projectName,
  requesterName,
  requesterEmail,
  organizationName,
  comments,
  startDate,
  endDate,
  slideCount,
  approvalLink,
}: BatchApprovalNotificationParams) {
  if (adminEmails.length === 0) {
    return { data: [], error: null };
  }

  const emailTemplate = ApprovalNotificationEmailTemplate({
    projectName,
    requesterName,
    requesterEmail,
    organizationName,
    comments,
    startDate,
    endDate,
    slideCount,
    approvalLink,
  });

  const text = await render(emailTemplate, { plainText: true });

  // Create batch email array
  const batchEmails = adminEmails.map((adminEmail) => ({
    from: emailFrom,
    to: adminEmail,
    subject: `New project approval request: ${projectName}`,
    text,
    replyTo: requesterEmail,
    react: emailTemplate,
  }));

  // Send all emails in a single batch request
  return await resend.batch.send(batchEmails);
}

type ApprovedNotificationEmailParams = {
  requesterEmail: string;
  projectName: string;
  requesterName: string;
  approverName: string;
  organizationName: string;
  reviewComments?: string;
  startDate?: string;
  endDate?: string;
  slideCount?: number;
  projectLink: string;
};

export async function sendApprovedNotificationEmail({
  requesterEmail,
  projectName,
  requesterName,
  approverName,
  organizationName,
  reviewComments,
  startDate,
  endDate,
  slideCount,
  projectLink,
}: ApprovedNotificationEmailParams) {
  const emailTemplate = ApprovedNotificationEmailTemplate({
    projectName,
    requesterName,
    approverName,
    organizationName,
    reviewComments,
    startDate,
    endDate,
    slideCount,
    projectLink,
  });

  const text = await render(emailTemplate, { plainText: true });

  return await resend.emails.send({
    subject: `🎉 Project Approved: ${projectName}`,
    from: emailFrom,
    to: requesterEmail,
    text,
    react: emailTemplate,
  });
}

type RejectedNotificationEmailParams = {
  requesterEmail: string;
  projectName: string;
  requesterName: string;
  approverName: string;
  organizationName: string;
  reviewComments?: string;
  startDate?: string;
  endDate?: string;
  slideCount?: number;
  projectLink: string;
};

export async function sendRejectedNotificationEmail({
  requesterEmail,
  projectName,
  requesterName,
  approverName,
  organizationName,
  reviewComments,
  startDate,
  endDate,
  slideCount,
  projectLink,
}: RejectedNotificationEmailParams) {
  const emailTemplate = RejectedNotificationEmailTemplate({
    projectName,
    requesterName,
    approverName,
    organizationName,
    reviewComments,
    startDate,
    endDate,
    slideCount,
    projectLink,
  });

  const text = await render(emailTemplate, { plainText: true });

  return await resend.emails.send({
    subject: `Project Not Approved: ${projectName}`,
    from: emailFrom,
    to: requesterEmail,
    text,
    react: emailTemplate,
  });
}
