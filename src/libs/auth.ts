import { env } from "@/env";
import {
  ac,
  admin as admin<PERSON><PERSON>,
  manager,
  member,
} from "@/libs/auth-permissions";
import { APP_NAME } from "@/libs/constants";
import {
  sendEmailChangeEmail,
  sendOrganizationInvitationEmail,
  sendPasswordResetEmail,
  sendVerifyEmail,
} from "@/libs/mail";
import { db } from "@/server/db";
import { betterAuth, type AuthContext } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { admin, openAPI, organization } from "better-auth/plugins";
import { passkey } from "better-auth/plugins/passkey";
import { headers } from "next/headers";

export const auth = betterAuth({
  database: prismaAdapter(db, {
    provider: "mysql",
  }),
  advanced: {
    generateId: false,
  },
  plugins: [
    openAPI(),
    admin({
      adminRoles: ["admin"],
    }),
    passkey({
      rpName: APP_NAME,
      rpID: env.RPID,
      origin: env.APP_BASE_URL,
    }),
    organization({
      ac,
      roles: {
        admin: admin<PERSON><PERSON>,
        manager,
        member,
      },
      dynamicAccessControl: {
        enabled: true,
      },
      async sendInvitationEmail(data) {
        console.log("Invitation Link: ", constructInviteLink(data));
        await sendOrganizationInvitationEmail({
          inviteeEmail: data.email,
          inviterName: data.inviter.user.name,
          inviterEmail: data.inviter.user.email,
          organizationName: data.organization.name,
          invitationLink: constructInviteLink(data),
        });
      },
    }),
  ],
  onAPIError: {
    onError: async (error: unknown, ctx: AuthContext) => {
      const errorContext = {
        error,
        baseURL: ctx.baseURL,
        session: ctx.session,
        trustedOrigins: ctx.trustedOrigins,
        request: {
          url: ctx.options.baseURL,
          method: ctx.options.basePath,
        },
      };
      console.info("Auth API error", errorContext);
    },
  },
  emailAndPassword: {
    enabled: true,
    sendResetPassword: async ({ user, url }) => {
      await sendPasswordResetEmail(user.email, url);
    },
  },
  user: {
    changeEmail: {
      enabled: true,
      sendChangeEmailVerification: async ({ user, newEmail, url }) => {
        await sendEmailChangeEmail(user.email, newEmail, url);
      },
    },
    deleteUser: {
      enabled: true,
    },
    additionalFields: {
      firstName: {
        type: "string",
        minLength: 2,
        maxLength: 50,
      },
      lastName: {
        type: "string",
        minLength: 2,
        maxLength: 50,
      },
    },
  },
  emailVerification: {
    sendOnSignUp: false,
    autoSignInAfterVerification: true,
    sendVerificationEmail: async ({ user, url }) => {
      await sendVerifyEmail(user.email, url);
    },
  },
  socialProviders: {
    google: {
      clientId: env.GOOGLE_CLIENT_ID,
      clientSecret: env.GOOGLE_CLIENT_SECRET,
    },
    github: {
      clientId: env.GITHUB_CLIENT_ID,
      clientSecret: env.GITHUB_CLIENT_SECRET,
    },
  },
  account: {
    accountLinking: {
      enabled: true,
      trustedProviders: ["google", "github", "email-password", "microsoft"],
    },
  },
});

export const getAuthSession = async () => {
  const session = await auth.api.getSession({ headers: await headers() });
  return { session: session?.session, user: session?.user };
};

function constructInviteLink(data: {
  id: string;
  email: string;
  role: string;
  organization: { name: string; id: string };
}) {
  const searchParams = new URLSearchParams({
    organization: data.organization.name,
    organizationId: data.organization.id,
    inviteId: data.id,
    email: data.email,
    role: data.role,
  });

  const inviteLink = `${env.APP_BASE_URL}/api/invitation?${searchParams.toString()}`;
  return inviteLink;
}
