"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  Di<PERSON>Title,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useGroupUpdateMutation } from "@/queries/groups.queries";
import type { GroupOutput } from "@/types/group.types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const editGroupSchema = z.object({
  name: z.string().min(1, "Group name is required"),
});

type EditGroupFormData = z.infer<typeof editGroupSchema>;

interface EditGroupDialogProps {
  open: boolean;
  onClose: () => void;
  group: GroupOutput;
}

export function EditGroupDialog({
  open,
  onClose,
  group,
}: EditGroupDialogProps) {
  const updateGroupMutation = useGroupUpdateMutation();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<EditGroupFormData>({
    resolver: zodResolver(editGroupSchema),
    defaultValues: {
      name: group.name,
    },
  });

  // Reset form when group changes
  useEffect(() => {
    reset({ name: group.name });
  }, [group, reset]);

  const onSubmit = async (data: EditGroupFormData) => {
    await updateGroupMutation.mutateAsync({
      id: group.id,
      ...data,
    });
    onClose();
  };

  const handleClose = () => {
    reset({ name: group.name });
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Group</DialogTitle>
          <DialogDescription>
            Update the group name and settings.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Input
              id="name"
              label="Group name"
              placeholder="Enter group name..."
              {...register("name")}
              error={!!errors.name}
              errorMessage={errors.name?.message}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={updateGroupMutation.isPending}
            >
              Cancel
            </Button>
            <Button type="submit" loading={updateGroupMutation.isPending}>
              Update group
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
