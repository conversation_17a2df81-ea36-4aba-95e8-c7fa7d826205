"use client";

import { But<PERSON> } from "@/components/ui/button";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDialog } from "@/hooks/use-dialog";
import { useGroupDeleteMutation } from "@/queries/groups.queries";
import type { GroupOutput } from "@/types/group.types";
import { IconDots, IconEdit, IconTrash, IconUsers } from "@tabler/icons-react";

interface GroupActionsMenuProps {
  group: GroupOutput;
  onEdit?: () => void;
  onManageMembers?: () => void;
}

export function GroupActionsMenu({
  group,
  onEdit,
  onManageMembers,
}: GroupActionsMenuProps) {
  const [deleteDialog, deleteDialogHandler] = useDialog();
  const deleteGroupMutation = useGroupDeleteMutation();

  const handleDelete = async () => {
    await deleteGroupMutation.mutateAsync({ id: group.id });
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="size-8 text-gray-400 data-[state=open]:bg-accent data-[state=open]:text-gray-900"
            title="Open group actions menu"
          >
            <IconDots size={18} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {onManageMembers && (
            <DropdownMenuItem onClick={onManageMembers}>
              <IconUsers size={16} className="mr-1" />
              Manage members
            </DropdownMenuItem>
          )}
          {onEdit && (
            <DropdownMenuItem onClick={onEdit}>
              <IconEdit size={16} className="mr-1" />
              Edit
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            onClick={deleteDialogHandler.open}
            className="text-red-600 focus:text-red-600"
          >
            <IconTrash size={16} className="mr-1" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <DeleteDialog
        title="Group"
        open={deleteDialog}
        onClose={deleteDialogHandler.close}
        onDelete={handleDelete}
        loading={deleteGroupMutation.isPending}
      />
    </>
  );
}
