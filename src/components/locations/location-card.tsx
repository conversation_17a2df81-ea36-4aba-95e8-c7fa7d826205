"use client";

import { LocationCardActionsMenu } from "@/components/locations/location-card-actions-menu";
import { SubLocationsList } from "@/components/locations/sublocations-list";
import { Card } from "@/components/ui/card";
import type { LocationOutput } from "@/types/location.types";
import type { Role } from "@/types/organization.types";
import {
  IconBuilding,
  IconFolder,
  IconLocation,
  IconMapPin,
} from "@tabler/icons-react";

export function LocationCard({
  location,
  userRole,
}: {
  location: LocationOutput;
  userRole: Role;
}) {
  const sublocationsCount = location?.sublocations?.length ?? 0;
  const hasActiveProjects = Number(location?._count?.projects ?? 0) > 0;

  return (
    <Card className="border border-gray-200 duration-150 hover:border-gray-300">
      <div className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 rounded-lg border border-blue-200 bg-blue-50 px-3 py-1.5 text-xs font-medium text-blue-700">
            <IconLocation className="h-3.5 w-3.5" />
            <span>Location</span>
          </div>
          {userRole === "admin" && (
            <LocationCardActionsMenu
              location={location}
              locationId={location?.id ?? ""}
              disabled={hasActiveProjects}
            />
          )}
        </div>

        <h3
          className="mt-4 truncate text-lg font-semibold text-gray-900"
          title={location?.name}
        >
          {location?.name}
        </h3>

        <div className="mt-3 flex items-center gap-1 text-sm text-gray-600">
          <IconMapPin className="h-4 w-4 flex-shrink-0" />
          <span className="truncate">
            {location?.address} {location?.city}, {location?.state}
            {location?.postalCode && ` ${location?.postalCode}`}
          </span>
        </div>

        <div className="flex items-center gap-4">
          {/* Projects count */}
          <div className="mt-3 flex min-w-0 items-center gap-2 text-sm text-gray-600">
            <IconFolder className="h-4 w-4" />
            <span
              className="truncate"
              title={`${location?._count?.projects ?? 0} active project${location?._count?.projects === 1 ? "" : "s"}`}
            >
              {location?._count?.projects ?? 0} active project
              {location?._count?.projects === 1 ? "" : "s"}
            </span>
          </div>

          {/* Sublocations */}
          <div className="mt-3 flex min-w-0 items-center gap-2 text-sm text-gray-600">
            <IconBuilding className="h-4 w-4" />
            <span
              className="truncate"
              title={`${sublocationsCount} sublocation${sublocationsCount === 1 ? "" : "s"}`}
            >
              {sublocationsCount} sublocation
              {sublocationsCount === 1 ? "" : "s"}
            </span>
          </div>
        </div>

        <div className="mt-4">
          <SubLocationsList
            locationId={location?.id ?? ""}
            sublocations={
              location?.sublocations?.map((sub) => ({
                ...sub,
                location: {
                  id: location.id,
                  name: location.name,
                  deletedAt: location.deletedAt,
                  createdAt: location.createdAt,
                  updatedAt: location.updatedAt,
                  organizationId: location.organizationId,
                  state: location.state,
                  shortId: location.shortId,
                  address: location.address,
                  city: location.city,
                  country: location.country,
                  postalCode: location.postalCode,
                },
              })) ?? []
            }
            disabled={userRole !== "admin"}
          />
        </div>
      </div>
    </Card>
  );
}
