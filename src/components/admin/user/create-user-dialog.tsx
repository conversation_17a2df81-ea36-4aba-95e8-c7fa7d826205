"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { z } from "@/libs/zod";
import { useCreateUser } from "@/queries/admin-user.queries";
import type { CreateUserParams } from "@/types/admin-user.types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

interface CreateUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface CreateUserForm {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: "admin" | "user";
}

export function CreateUserDialog({
  open,
  onOpenChange,
}: CreateUserDialogProps) {
  const createUserMutation = useCreateUser();

  // Zod schema for form validation
  const createUserSchema = z
    .object({
      firstName: z.string().min(1, "First name is required"),
      lastName: z.string().min(1, "Last name is required"),
      email: z.string().email("Please enter a valid email address"),
      password: z.string().min(8, "Password must be at least 8 characters"),
      confirmPassword: z.string().min(1, "Please confirm your password"),
      role: z.enum(["admin", "user"]),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: "Passwords do not match",
      path: ["confirmPassword"],
    });

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<CreateUserForm>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
      role: "user",
    },
  });

  const onSubmit = async (data: CreateUserForm) => {
    try {
      const params: CreateUserParams = {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        password: data.password,
        role: data.role,
      };

      await createUserMutation.mutateAsync(params);
      toast.success("User created successfully");
      reset();
      onOpenChange(false);
    } catch (error) {
      console.error("Error creating user:", error);
      toast.error("Failed to create user");
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      reset();
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create a new user</DialogTitle>
          <DialogDescription>
            Add a new user to the system. They will receive an email with their
            account details.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid gap-4">
            <div className="grid grid-cols-2 gap-2">
              <Input
                id="firstName"
                label="First name"
                placeholder="Enter first name"
                error={!!errors.firstName}
                errorMessage={errors.firstName?.message}
                {...register("firstName")}
              />
              <Input
                id="lastName"
                label="Last name"
                placeholder="Enter last name"
                error={!!errors.lastName}
                errorMessage={errors.lastName?.message}
                {...register("lastName")}
              />
            </div>

            <div className="grid gap-2">
              <Input
                id="email"
                label="Email"
                type="email"
                placeholder="Enter email address"
                error={!!errors.email}
                errorMessage={errors.email?.message}
                {...register("email")}
              />
            </div>

            <div className="grid gap-2">
              <Input
                id="password"
                label="Password"
                type="password"
                placeholder="Enter password"
                error={!!errors.password}
                errorMessage={errors.password?.message}
                {...register("password")}
              />
            </div>

            <div className="grid gap-2">
              <Input
                id="confirmPassword"
                label="Confirm Password"
                type="password"
                placeholder="Confirm password"
                error={!!errors.confirmPassword}
                errorMessage={errors.confirmPassword?.message}
                {...register("confirmPassword")}
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="role" className="text-sm font-medium">
                Role
              </label>
              <Select
                onValueChange={(value: "admin" | "user") =>
                  setValue("role", value)
                }
                defaultValue="user"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              loading={isSubmitting}
            >
              Create user
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
