import type { Slide } from "@/types/slide.types";
import { IconExclamationMark } from "@tabler/icons-react";
import Image from "next/image";

interface SlideProps {
  slide: Slide | undefined;
}

export function SlidePreview({ slide }: SlideProps) {
  return (
    <div className="flex h-[90vh] flex-col items-center justify-center">
      {slide?.isImportant && (
        <div className="mb-4 flex items-center gap-2 rounded-full bg-red-500 px-4 py-2 text-white shadow-lg">
          <div className="flex size-5 items-center justify-center rounded-full bg-white text-sm font-bold text-red-500">
            <IconExclamationMark size={16} />
          </div>
          <span className="font-semibold">Important Slide</span>
        </div>
      )}
      <div className="flex h-full w-full flex-col items-center justify-center">
        <div className="relative h-[70vh] max-h-[800px] w-[90%]">
          {slide && (
            <Image
              src={slide.imageUrl ?? "/placeholder.svg"}
              alt={""}
              fill
              sizes="(max-width: 768px) 100vw, 1400px"
              className="object-contain"
              priority
            />
          )}
        </div>
      </div>
    </div>
  );
}
