import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import type { z } from "@/libs/zod";
import { useProjectResubmitMutation } from "@/queries/project.queries";
import { ProjectResubmissionSchema } from "@/schemas/approval.schemas";
import { api } from "@/trpc/react";
import type { Project } from "@/types/project.types";
import { formatDate } from "@/utils/format-date";
import { zodResolver } from "@hookform/resolvers/zod";
import { IconInfoCircle, IconX } from "@tabler/icons-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";

type FormData = z.infer<typeof ProjectResubmissionSchema>;

interface ProjectResubmissionDialogProps extends DialogProps {
  onClose: () => void;
  project: Project;
}

export function ProjectResubmissionDialog({
  open,
  onClose,
  project,
}: ProjectResubmissionDialogProps) {
  const [showRejectionHistory, setShowRejectionHistory] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
  } = useForm<FormData>({
    resolver: zodResolver(ProjectResubmissionSchema),
    defaultValues: {
      projectId: project.id,
      name: project.name,
      startDate: project.startDate ? formatDate(project.startDate) : "",
      endDate: project.endDate ? formatDate(project.endDate) : "",
    },
  });

  const resubmitMutation = useProjectResubmitMutation();
  const rejectionHistory = api.approvals.getRejectionHistory.useQuery(
    { projectId: project.id },
    { enabled: open && showRejectionHistory },
  );

  const closeModal = () => {
    reset();
    setShowRejectionHistory(false);
    onClose();
  };

  useEffect(() => {
    if (open) {
      setValue("projectId", project.id);
      setValue("name", project.name);
      setValue(
        "startDate",
        project.startDate ? formatDate(project.startDate) : "",
      );
      setValue("endDate", project.endDate ? formatDate(project.endDate) : "");
    }
  }, [open, project, setValue]);

  async function onSubmit(data: FormData) {
    await resubmitMutation.mutateAsync(data);
    closeModal();
  }

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Resubmit Project for Approval</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Rejection Alert */}
          <Alert variant="destructive">
            <IconX className="h-4 w-4" />
            <AlertTitle>Project Rejected</AlertTitle>
            <AlertDescription>
              This project was rejected during the approval process. Please
              review the feedback, make necessary changes, and resubmit for
              approval.
            </AlertDescription>
          </Alert>

          {/* View Rejection History */}
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Project Details</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowRejectionHistory(!showRejectionHistory)}
              leftIcon={<IconInfoCircle className="size-4" />}
            >
              {showRejectionHistory ? "Hide" : "View"} Rejection History
            </Button>
          </div>

          {/* Rejection History */}
          {showRejectionHistory && (
            <div className="space-y-3 rounded-lg border p-4">
              <h4 className="text-sm font-medium">Rejection History</h4>
              {rejectionHistory.isLoading && (
                <p className="text-sm text-muted-foreground">
                  Loading history...
                </p>
              )}
              {rejectionHistory.data?.map((rejection, index) => (
                <div
                  key={index}
                  className="border-l-2 border-red-200 py-2 pl-4"
                >
                  <div className="mb-1 flex items-center gap-2">
                    <Badge variant="red">Rejected</Badge>
                    <span className="text-sm text-muted-foreground">
                      {formatDate(rejection.createdAt)}
                    </span>
                  </div>
                  {rejection.reviewComments && (
                    <p className="text-sm">{rejection.reviewComments}</p>
                  )}
                </div>
              ))}
              {rejectionHistory.data?.length === 0 && (
                <p className="text-sm text-muted-foreground">
                  No rejection history found.
                </p>
              )}
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div>
                <Input
                  id="name"
                  label="Project Name"
                  {...register("name")}
                  error={!!errors.name}
                  errorMessage={errors.name?.message}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Input
                    id="startDate"
                    label="Start Date"
                    type="date"
                    {...register("startDate")}
                    error={!!errors.startDate}
                    errorMessage={errors.startDate?.message}
                  />
                </div>
                <div>
                  <Input
                    id="endDate"
                    label="End Date"
                    type="date"
                    {...register("endDate")}
                    error={!!errors.endDate}
                    errorMessage={errors.endDate?.message}
                  />
                </div>
              </div>

              <div>
                <Textarea
                  id="comments"
                  label="Comments (Optional)"
                  {...register("comments")}
                  placeholder="Add any additional comments or notes about changes made..."
                  error={!!errors.comments}
                  errorMessage={errors.comments?.message}
                />
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={closeModal}>
                Cancel
              </Button>
              <Button type="submit" loading={isSubmitting}>
                Resubmit for Approval
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
