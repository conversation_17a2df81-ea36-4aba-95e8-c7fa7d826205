"use client";

import { PermissionGuard } from "@/components/permission-guard";
import { ProjectConfirmEditDialog } from "@/components/projects/project-confirm-edit-dialog";
import { ProjectStatusBadge } from "@/components/projects/project-status-badge";
import { ProjectSubmitApprovalButton } from "@/components/projects/project-submit-approval-button";
import { ProjectViewActionsMenu } from "@/components/projects/project-view-actions-menu";
import { SlidePreview } from "@/components/projects/slide-preview";
import { SlidesImageUploader } from "@/components/projects/slides-image-uploader";
import { SlidesSidebar } from "@/components/projects/slides-sidebar";
import { AlertError } from "@/components/ui/alert-error";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePicker } from "@/components/ui/date-picker";
import { EmptyState } from "@/components/ui/empty-state";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PageLoader } from "@/components/ui/page-structure";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { useDebouncedState } from "@/hooks/use-debounced-state";
import { useDialog } from "@/hooks/use-dialog";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import { cn } from "@/libs/utils";
import {
  useGroupMembers,
  useGroups as useOrgGroups,
} from "@/queries/groups.queries";
import { useLocationAssignmentsForMember } from "@/queries/location-assignment.queries";
import { useLocations } from "@/queries/location.queries";
import { useOrganizationBySlug } from "@/queries/organization.queries";
import {
  useProjectById,
  useProjectPublishMutation,
  useProjectUpdateMutation,
} from "@/queries/project.queries";
import { useSlideAddManyMutation, useSlides } from "@/queries/slide.queries";
import type { ProjectStatus } from "@/types/project.types";
import type { Slide } from "@/types/slide.types";
import { createImageUploadUrl } from "@/utils/create-image-upload-url";
import { isEmpty } from "@/utils/is-empty";
import {
  IconBuilding,
  IconChevronDown,
  IconChevronLeft,
  IconChevronRight,
  IconMapPin,
  IconPencil,
  IconPlayerPlay,
  IconPlus,
  IconSearch,
  IconSend,
} from "@tabler/icons-react";
import type { UploadedFile } from "better-upload/client";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

interface Props {
  projectId: string;
}

export function ProjectView({ projectId }: Props) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const slug = useOrganizationSlug();
  const [uploaderDialog, uploaderDialogHandlers] = useDialog();
  const [publishSheetDialog, publishSheetDialogHandlers] = useDialog();
  const [confirmEditDialog, confirmEditDialogHandlers] = useDialog();
  const [filesUploadingLength, setFilesUploadingLength] = useState(0);
  const [currentSlideId, setCurrentSlideId] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [publishError, setPublishError] = useState("");
  // Group/Member filters
  const [selectedGroupId, setSelectedGroupId] = useState("");
  const [selectedMemberId, setSelectedMemberId] = useState("");
  // Location/Sublocation filters
  const [selectedLocations, setSelectedLocations] = useState<Set<string>>(
    new Set(),
  );
  const [selectedSublocations, setSelectedSublocations] = useState<Set<string>>(
    new Set(),
  );
  const [expandedLocations, setExpandedLocations] = useState<Set<string>>(
    new Set(),
  );

  const pageAction = searchParams.get("action");

  const organization = useOrganizationBySlug(slug);
  const locationsQuery = useLocations({
    organizationId: organization.data?.id ?? "",
    take: 500,
  });
  const groupsQuery = useOrgGroups({
    organizationId: organization.data?.id ?? "",
    take: 500,
  });
  const groupMembersQuery = useGroupMembers({
    groupId: selectedGroupId || undefined,
    take: 500,
  });
  const assignmentsQuery = useLocationAssignmentsForMember(
    organization.data?.id ?? "",
    selectedMemberId,
  );
  const project = useProjectById(projectId);
  const slidesQuery = useSlides(projectId);
  const [slides, setSlides] = useState<Slide[]>([]);

  // Clear selections when selected member changes
  useEffect(() => {
    setSelectedLocations(new Set());
    setSelectedSublocations(new Set());
    setExpandedLocations(new Set());
  }, [selectedMemberId]);

  const [projectName, setProjectName] = useDebouncedState(
    project.data?.name,
    250,
  );

  const projectUpdateMutation = useProjectUpdateMutation();
  const publishProjectMutation = useProjectPublishMutation();

  const isProjectNotEditing = project.data?.status !== "editing";

  // This is used to update the project name when the the debounced projectName state is updated
  useEffect(() => {
    async function updateProjectName() {
      return await projectUpdateMutation.mutateAsync({
        id: projectId,
        name: projectName,
      });
    }
    if (!isEmpty(projectName)) {
      updateProjectName();
    }
  }, [projectName, projectId]);

  // This is used to update the slides when the slides query is updated
  useEffect(() => {
    if (slidesQuery.data?.data) {
      setSlides(slidesQuery.data?.data);
    }
  }, [slidesQuery.data?.data]);

  const currentSlideById =
    slides.find((slide) => slide.id === currentSlideId) ?? slides[0];

  const addSlidesMutation = useSlideAddManyMutation();

  const addNewSlides = async (files: UploadedFile[]) => {
    uploaderDialogHandlers.close();
    setFilesUploadingLength(files.length);
    const slidesToAdd = files.map((file) => ({
      imageUrl: createImageUploadUrl(file.objectKey),
      objectKey: file.objectKey,
    }));
    await addSlidesMutation.mutateAsync({
      projectId,
      lastSlideId: slides.at(-1)?.id ?? "",
      images: slidesToAdd,
    });
    setCurrentSlideId(slides.at(-1)?.id ?? "");
  };

  // Filter locations based on the search query and selected member assignments
  const filteredLocations = (() => {
    const all =
      locationsQuery?.data?.data?.filter((location) =>
        [
          location.name,
          location.address,
          location.city,
          location.state,
          location.postalCode,
          location.country,
          location.id,
          ...(location.sublocations?.map((s) => s.name) ?? []),
        ]
          .filter(Boolean)
          .some((val) =>
            String(val).toLowerCase().includes(searchQuery.toLowerCase()),
          ),
      ) ?? [];

    if (selectedMemberId && assignmentsQuery.data) {
      const assignedIds = new Set(
        assignmentsQuery.data.map((a: any) => a.locationId ?? a.location?.id),
      );
      return all.filter((loc) => assignedIds.has(loc.id));
    }
    return all;
  })();

  // Handle location expansion
  const toggleLocationExpanded = (locationId: string) => {
    const newExpanded = new Set(expandedLocations);
    if (newExpanded.has(locationId)) {
      newExpanded.delete(locationId);
    } else {
      newExpanded.add(locationId);
    }
    setExpandedLocations(newExpanded);
  };

  // Handle location selection
  const handleLocationSelect = (locationId: string, checked: boolean) => {
    const newSelectedLocations = new Set(selectedLocations);
    const newSelectedSublocations = new Set(selectedSublocations);
    const newExpandedLocations = new Set(expandedLocations);

    if (checked) {
      newSelectedLocations.add(locationId);
      // Automatically expand when location is selected
      newExpandedLocations.add(locationId);
      // When selecting a location, remove any individual sublocations for that location
      const location = locationsQuery?.data?.data?.find(
        (loc) => loc.id === locationId,
      );
      location?.sublocations?.forEach((sub) => {
        newSelectedSublocations.delete(sub.id);
      });
    } else {
      newSelectedLocations.delete(locationId);
      // When deselecting a location, also remove all its sublocations
      const location = locationsQuery?.data?.data?.find(
        (loc) => loc.id === locationId,
      );
      location?.sublocations?.forEach((sub) => {
        newSelectedSublocations.delete(sub.id);
      });
    }

    setSelectedLocations(newSelectedLocations);
    setSelectedSublocations(newSelectedSublocations);
    setExpandedLocations(newExpandedLocations);
  };

  // Handle sublocation selection
  const handleSublocationSelect = (
    locationId: string,
    sublocationId: string,
    checked: boolean,
  ) => {
    const newSelectedLocations = new Set(selectedLocations);
    const newSelectedSublocations = new Set(selectedSublocations);

    if (checked) {
      newSelectedSublocations.add(sublocationId);
      // Always select the parent location when any sublocation is selected
      newSelectedLocations.add(locationId);
    } else {
      newSelectedSublocations.delete(sublocationId);
      // Check if any other sublocations for this location are still selected
      const location = locationsQuery?.data?.data?.find(
        (loc) => loc.id === locationId,
      );
      const hasOtherSelectedSublocations = location?.sublocations?.some(
        (sub) =>
          sub.id !== sublocationId && newSelectedSublocations.has(sub.id),
      );
      // Only deselect the location if no sublocations are selected
      if (!hasOtherSelectedSublocations) {
        newSelectedLocations.delete(locationId);
      }
    }

    setSelectedLocations(newSelectedLocations);
    setSelectedSublocations(newSelectedSublocations);
  };

  // Check if location is selected
  const isLocationSelected = (locationId: string) => {
    return selectedLocations.has(locationId);
  };

  // Check if sublocation is selected
  const isSublocationSelected = (
    _locationId: string,
    sublocationId: string,
  ) => {
    return selectedSublocations.has(sublocationId);
  };

  // Check if location has any selected sublocations
  const hasSelectedSublocations = (locationId: string) => {
    const location = locationsQuery?.data?.data?.find(
      (loc) => loc.id === locationId,
    );
    return location?.sublocations?.some((sub) =>
      selectedSublocations.has(sub.id),
    );
  };

  // Handle select all functionality
  const isAllSelected =
    filteredLocations.length > 0 &&
    filteredLocations.every((location) => {
      const locSelected = selectedLocations.has(location.id);
      const allSubsSelected =
        location.sublocations?.every((sub) =>
          selectedSublocations.has(sub.id),
        ) ?? true;
      return locSelected && allSubsSelected;
    });

  const handleSelectAll = (checked: boolean) => {
    const newSelectedLocations = new Set(selectedLocations);
    const newSelectedSublocations = new Set(selectedSublocations);

    if (checked) {
      filteredLocations.forEach((location) => {
        newSelectedLocations.add(location.id);
        // Also select all sublocations for each location
        location.sublocations?.forEach((sub) => {
          newSelectedSublocations.add(sub.id);
        });
      });
    } else {
      filteredLocations.forEach((location) => {
        newSelectedLocations.delete(location.id);
        // Also deselect all sublocations for each location
        location.sublocations?.forEach((sub) => {
          newSelectedSublocations.delete(sub.id);
        });
      });
    }

    setSelectedLocations(newSelectedLocations);
    setSelectedSublocations(newSelectedSublocations);
  };

  function validatePublishValues() {
    if (!startDate && !endDate) {
      return {
        error: true,
        errorMessage: "Please select a start and end date",
      };
    }
    if (startDate && endDate && startDate > endDate) {
      return {
        error: true,
        errorMessage: "Start date must be before end date",
      };
    }
    if (selectedLocations.size === 0 || selectedSublocations.size === 0) {
      return {
        error: true,
        errorMessage: "Please select at least one location and sublocation",
      };
    }
    return { error: false, errorMessage: "" };
  }

  async function handlePublishProject() {
    setPublishError("");

    const validation = validatePublishValues();
    if (validation.error) {
      setPublishError(validation.errorMessage);
      return;
    }

    const data = {
      id: projectId,
      startDate: startDate?.toISOString() ?? "",
      endDate: endDate?.toISOString() ?? "",
      locations: Array.from(selectedLocations),
      sublocations: Array.from(selectedSublocations),
    };

    await publishProjectMutation.mutateAsync(data);
  }

  function resetPublishSheet() {
    setPublishError("");
    setSelectedLocations(new Set());
    setSelectedSublocations(new Set());
    setExpandedLocations(new Set());
    setSearchQuery("");
    setStartDate(null);
    setEndDate(null);
    setSelectedGroupId("");
    setSelectedMemberId("");
  }

  function closePublishSheetDialog() {
    if (pageAction === "publish") {
      resetPublishSheet();
      router.replace(`/${slug}/projects/${projectId}`);
      publishSheetDialogHandlers.close();
    }
    resetPublishSheet();
    publishSheetDialogHandlers.close();
  }

  if (project.isLoading || slidesQuery.isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="relative flex h-full flex-col overflow-y-hidden">
      <header className="absolute top-0 right-0 left-0 flex h-14 items-center justify-between border-b px-4 lg:px-4">
        <div className="flex items-center gap-4">
          <Button
            size="icon"
            href={`/${slug}/projects`}
            variant="secondary"
            className="size-8"
          >
            <IconChevronLeft size={16} />
          </Button>
          <Input
            defaultValue={project?.data?.name}
            className="w-full rounded-none border-0 p-0 text-xl font-semibold shadow-none ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 disabled:opacity-100 sm:w-[500px] sm:text-xl"
            onChange={(e) => {
              setProjectName(e.target.value);
            }}
          />
        </div>
        <div className="flex items-center gap-2">
          <ProjectStatusBadge
            status={project?.data?.status as ProjectStatus}
            className="mr-2"
          />
          {isProjectNotEditing ? (
            <Button
              variant="outline"
              leftIcon={<IconPencil size={16} />}
              onClick={confirmEditDialogHandlers.open}
            >
              Edit project
            </Button>
          ) : (
            <Button
              onClick={uploaderDialogHandlers.open}
              leftIcon={<IconPlus size={16} />}
            >
              Add Slides
            </Button>
          )}
          <Button
            variant="outline"
            leftIcon={<IconPlayerPlay size={16} />}
            href={`/preview?projectId=${projectId}`}
            target="_blank"
            rel="noreferrer noopener"
          >
            View preview
          </Button>
          {!isProjectNotEditing && (
            <Sheet
              open={publishSheetDialog || pageAction === "publish"}
              onOpenChange={closePublishSheetDialog}
            >
              <Button
                variant="outline"
                leftIcon={<IconSend size={16} />}
                onClick={publishSheetDialogHandlers.open}
                disabled={slides.length === 0}
              >
                Publish
              </Button>

              <SheetContent className="flex h-full w-full flex-col sm:max-w-[600px]">
                <SheetHeader className="flex-shrink-0">
                  <SheetTitle className="text-lg">
                    Schedule your project to be published
                  </SheetTitle>
                  <SheetDescription className="max-w-[450px] text-sm">
                    Publishing your project will make it available to the public
                    and will be available to view in your store locations and
                    sublocations.
                  </SheetDescription>
                </SheetHeader>

                <div className="flex flex-1 flex-col overflow-hidden px-4">
                  <AlertError message={publishError} className="mb-4" />

                  <div className="flex-shrink-0">
                    <p className="text-base font-semibold">Project runtime</p>
                    <div className="mt-2 flex w-full items-center justify-between gap-3">
                      <DatePicker
                        label="Start date"
                        className="w-full"
                        value={startDate}
                        onSelect={(date) => setStartDate(date ?? null)}
                        error={!!startDate && !!endDate && startDate > endDate}
                        errorMessage="Start date must be before end date"
                      />
                      <DatePicker
                        label="End date"
                        className="w-full"
                        value={endDate}
                        onSelect={(date) => setEndDate(date ?? null)}
                        error={!!startDate && !!endDate && startDate > endDate}
                        errorMessage="End date must be after start date"
                      />
                    </div>
                  </div>

                  <div className="mt-8 flex flex-1 flex-col overflow-hidden">
                    <div className="flex-shrink-0">
                      <p className="text-base font-semibold">
                        Choose locations and sublocations
                      </p>
                      <PermissionGuard spec={{ project: ["publish"] }}>
                        {/* Group and Member Filters */}
                        <div className="mt-3 grid grid-cols-1 gap-3 sm:grid-cols-2">
                          <div>
                            <Label className="mb-1 block text-sm font-medium">
                              Group
                            </Label>
                            <Select
                              value={selectedGroupId || "all_groups"}
                              onValueChange={(v) => {
                                // If "all_groups" is selected, clear the group ID and the member ID
                                setSelectedGroupId(v === "all_groups" ? "" : v);
                                setSelectedMemberId("");
                              }}
                              disabled={!groupsQuery.data}
                            >
                              <SelectTrigger aria-label="Select group">
                                <SelectValue placeholder="Select a group" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all_groups">
                                  All groups
                                </SelectItem>
                                {(groupsQuery.data?.data ?? []).map((group) => (
                                  <SelectItem key={group.id} value={group.id}>
                                    {group.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div>
                            <Label className="mb-1 block text-sm font-medium">
                              Member
                            </Label>
                            <Select
                              value={selectedMemberId || "all_members"}
                              onValueChange={(v) =>
                                // If "all_members" is selected, clear the member ID
                                setSelectedMemberId(
                                  v === "all_members" ? "" : v,
                                )
                              }
                              disabled={
                                !selectedGroupId || !groupMembersQuery.data
                              }
                            >
                              <SelectTrigger aria-label="Select member">
                                <SelectValue placeholder="Select a member" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all_members">
                                  All members
                                </SelectItem>
                                {(groupMembersQuery.data?.data ?? []).map(
                                  (groupMember) => (
                                    <SelectItem
                                      key={groupMember.member.id}
                                      value={groupMember.member.id}
                                    >
                                      {groupMember.member.user.name}
                                    </SelectItem>
                                  ),
                                )}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </PermissionGuard>
                      <div className="mt-4">
                        <Input
                          className="w-full"
                          icon={<IconSearch size={16} />}
                          placeholder="Filter locations and sublocations"
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                        />
                      </div>
                      <div className="mt-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Checkbox
                              id="select-all"
                              checked={isAllSelected}
                              onCheckedChange={(checked) =>
                                handleSelectAll(!!checked)
                              }
                            />
                            <Label
                              htmlFor="select-all"
                              className="cursor-pointer"
                            >
                              Select all locations and sublocations
                            </Label>
                          </div>
                          <PermissionGuard spec={{ project: ["publish"] }}>
                            <Button
                              href={`/${slug}/locations`}
                              variant="outline"
                              size="xs"
                              leftIcon={<IconPlus size={14} />}
                            >
                              Add locations
                            </Button>
                          </PermissionGuard>
                        </div>
                      </div>
                    </div>

                    <ScrollArea className="mt-4 flex-1 sm:pr-3">
                      <div className="space-y-3">
                        {filteredLocations.map((location) => (
                          <div
                            key={location.id}
                            className={cn(
                              `cursor-pointer rounded-lg border transition-colors ${
                                isLocationSelected(location.id) ||
                                hasSelectedSublocations(location.id)
                                  ? "border-gray-200 bg-gray-50 hover:bg-gray-100"
                                  : "border-gray-200 bg-card hover:bg-gray-50"
                              }`,
                            )}
                            onClick={() =>
                              handleLocationSelect(
                                location.id,
                                !isLocationSelected(location.id),
                              )
                            }
                          >
                            {/* Location Header */}
                            <div className="flex items-center gap-3 p-3">
                              <Checkbox
                                id={`location-${location.id}`}
                                checked={isLocationSelected(location.id)}
                                onCheckedChange={(checked) =>
                                  handleLocationSelect(location.id, !!checked)
                                }
                                onClick={(e) => e.stopPropagation()}
                              />
                              <div className="min-w-0 flex-1">
                                <div className="flex items-center gap-2">
                                  <IconMapPin
                                    size={16}
                                    className="flex-shrink-0 text-gray-500"
                                  />
                                  <div className="font-medium text-card-foreground">
                                    {location.name}
                                  </div>
                                </div>
                                <div className="mt-1 text-xs text-muted-foreground">
                                  {location.address} {location.city},{" "}
                                  {location.state}
                                </div>
                              </div>
                              {location.sublocations &&
                                location.sublocations.length > 0 && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toggleLocationExpanded(location.id);
                                    }}
                                    className="h-8 w-8 p-0"
                                  >
                                    {expandedLocations.has(location.id) ? (
                                      <IconChevronDown size={16} />
                                    ) : (
                                      <IconChevronRight size={16} />
                                    )}
                                  </Button>
                                )}
                            </div>

                            {/* Sublocations */}
                            {location.sublocations &&
                              location.sublocations.length > 0 &&
                              expandedLocations.has(location.id) && (
                                <div className="border-t border-gray-200 px-3 py-2">
                                  {location.sublocations.map((sublocation) => (
                                    <div
                                      key={sublocation.id}
                                      className={cn(
                                        `flex cursor-pointer items-center gap-3 rounded py-2 pl-6 transition-colors ${
                                          isSublocationSelected(
                                            location.id,
                                            sublocation.id,
                                          )
                                            ? "bg-gray-100 hover:bg-gray-200"
                                            : "hover:bg-gray-50"
                                        }`,
                                      )}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleSublocationSelect(
                                          location.id,
                                          sublocation.id,
                                          !isSublocationSelected(
                                            location.id,
                                            sublocation.id,
                                          ),
                                        );
                                      }}
                                    >
                                      <Checkbox
                                        id={`sublocation-${sublocation.id}`}
                                        checked={isSublocationSelected(
                                          location.id,
                                          sublocation.id,
                                        )}
                                        onCheckedChange={(checked) =>
                                          handleSublocationSelect(
                                            location.id,
                                            sublocation.id,
                                            !!checked,
                                          )
                                        }
                                        onClick={(e) => e.stopPropagation()}
                                      />
                                      <div className="flex flex-1 items-center gap-2">
                                        <IconBuilding
                                          size={14}
                                          className="text-gray-400"
                                        />
                                        <span className="text-sm text-gray-700">
                                          {sublocation.name}
                                        </span>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </div>

                <SheetFooter className="flex-shrink-0 border-t border-gray-200 p-4 sm:flex-row">
                  <PermissionGuard
                    spec={{ project: ["publish"] }}
                    fallback={
                      <ProjectSubmitApprovalButton
                        projectId={projectId}
                        projectName={project.data?.name || ""}
                        startDate={startDate}
                        endDate={endDate}
                        selectedLocations={selectedLocations}
                        selectedSublocations={selectedSublocations}
                      />
                    }
                  >
                    <Button
                      onClick={handlePublishProject}
                      loading={publishProjectMutation.isPending}
                    >
                      Publish project
                    </Button>
                  </PermissionGuard>
                  <SheetClose asChild>
                    <Button variant="outline">Cancel</Button>
                  </SheetClose>
                </SheetFooter>
              </SheetContent>
            </Sheet>
          )}

          <ProjectViewActionsMenu projectId={projectId} />
        </div>
      </header>

      <div className="absolute top-14 right-0 bottom-0 left-0 flex flex-1 overflow-hidden">
        <SlidesSidebar
          slides={slides}
          projectStatus={project.data?.status as ProjectStatus}
          currentSlideId={currentSlideId || (currentSlideById?.id ?? "")}
          onSelectSlideById={setCurrentSlideId}
          isPending={addSlidesMutation.isPending}
          slidesUploadingLength={filesUploadingLength}
        />
        <>
          {isEmpty(slides) && (
            <div className="flex h-full w-full flex-col items-center justify-center">
              <EmptyState
                title="No slides yet"
                subtitle="Add some new slides to get started."
                icon={<IconPlus size={40} />}
                actionButton={
                  <Button
                    leftIcon={<IconPlus size={16} />}
                    onClick={uploaderDialogHandlers.open}
                  >
                    Add Slides
                  </Button>
                }
              />
            </div>
          )}
          {!isEmpty(slides) && (
            <main className="flex flex-1 flex-col overflow-y-auto p-6">
              <SlidePreview slide={currentSlideById} />
            </main>
          )}
        </>
      </div>
      <SlidesImageUploader
        open={uploaderDialog}
        onClose={uploaderDialogHandlers.close}
        onUploadComplete={addNewSlides}
        organizationId={organization.data?.id ?? ""}
      />
      <ProjectConfirmEditDialog
        open={confirmEditDialog}
        onClose={confirmEditDialogHandlers.close}
        projectId={projectId}
      />
    </div>
  );
}
