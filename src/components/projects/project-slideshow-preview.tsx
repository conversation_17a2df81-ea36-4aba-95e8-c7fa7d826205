"use client";

import { Button } from "@/components/ui/button";
import type { SlideshowPreviewSlide } from "@/types/slide.types";
import { IconChevronLeft, IconChevronRight } from "@tabler/icons-react";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";

interface Props {
  slides: SlideshowPreviewSlide[];
  autoPlayInterval?: number;
}

export function ProjectSlideshowPreview({
  slides,
  autoPlayInterval = 10000,
}: Props) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Controls visibility
  const [controlsVisible, setControlsVisible] = useState(true);
  const hideControlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const revealControls = () => {
    setControlsVisible(true);
    if (hideControlsTimeoutRef.current)
      clearTimeout(hideControlsTimeoutRef.current);
    hideControlsTimeoutRef.current = setTimeout(
      () => setControlsVisible(false),
      3000,
    );
  };

  const goPrev = () => {
    revealControls();
    setCurrentIndex((prev) => (prev - 1 + slides.length) % slides.length);
  };
  const goNext = () => {
    revealControls();
    setCurrentIndex((prev) => (prev + 1) % slides.length);
  };

  // Hide controls after 3 seconds of inactivity
  useEffect(() => {
    revealControls();
    return () => {
      if (hideControlsTimeoutRef.current)
        clearTimeout(hideControlsTimeoutRef.current);
    };
  }, []);

  // Auto-play functionality with per-slide duration
  useEffect(() => {
    if (intervalRef.current) clearTimeout(intervalRef.current);
    if (slides.length <= 1) return;

    let durationMs = autoPlayInterval;
    if (
      slides[currentIndex]?.duration &&
      typeof slides[currentIndex].duration === "number"
    ) {
      durationMs = slides[currentIndex].duration * 1000;
    }

    intervalRef.current = setTimeout(() => {
      setCurrentIndex((prev) => (prev + 1) % slides.length);
    }, durationMs);

    return () => {
      if (intervalRef.current) clearTimeout(intervalRef.current);
    };
  }, [slides.length, currentIndex, autoPlayInterval]);

  // Preload the next image to prevent hiccups during fade transitions
  useEffect(() => {
    if (slides.length < 2) return;
    const nextIndex = (currentIndex + 1) % slides.length;
    const img = new window.Image();
    img.src = slides[nextIndex]?.imageUrl ?? "";
  }, [currentIndex, slides]);

  if (!slides.length) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-black lg:w-screen">
        <p className="text-4xl font-medium text-white">No slides to display</p>
      </div>
    );
  }

  return (
    <div
      className="relative h-screen w-full overflow-hidden bg-black lg:w-screen"
      onMouseMove={revealControls}
      onTouchStart={revealControls}
    >
      {slides.map((slide, index) => (
        <div
          key={slide.id}
          className={`absolute inset-0 flex h-full w-full items-center justify-center p-16 transition-opacity duration-1000 ease-in-out lg:p-0 ${
            index === currentIndex ? "opacity-100" : "opacity-0"
          }`}
        >
          <div className="relative h-full w-full">
            <Image
              src={slide.imageUrl}
              alt={`Slide ${index + 1}`}
              className="h-full w-full object-contain"
              priority
              fill
            />
            {/* Important slide border overlay */}
            {index === currentIndex && slide.isImportant && (
              <div className="pointer-events-none absolute inset-0 animate-pulse border-8 border-red-500" />
            )}
          </div>
        </div>
      ))}

      {slides.length > 1 && (
        <div
          className={`absolute inset-0 flex items-center justify-between px-2 transition-opacity duration-300 sm:px-4 ${controlsVisible ? "pointer-events-auto opacity-100" : "pointer-events-none opacity-0"}`}
        >
          <Button
            size="icon"
            variant="ghost"
            className="pointer-events-auto size-14 rounded-full bg-neutral-700/80 p-3 text-white shadow-lg shadow-black/20 backdrop-blur-sm hover:bg-neutral-600/90 hover:text-white sm:size-12 sm:p-2"
            aria-label="Previous slide"
            onClick={goPrev}
          >
            <IconChevronLeft className="size-7 sm:size-6" />
          </Button>
          <Button
            size="icon"
            variant="ghost"
            className="pointer-events-auto size-14 rounded-full bg-neutral-700/80 p-3 text-white shadow-lg shadow-black/20 backdrop-blur-sm hover:bg-neutral-600/90 hover:text-white sm:size-12 sm:p-2"
            aria-label="Next slide"
            onClick={goNext}
          >
            <IconChevronRight className="size-7 sm:size-6" />
          </Button>
        </div>
      )}

      {/* Slide index indicator */}
      <div
        className={`pointer-events-none absolute bottom-6 left-1/2 -translate-x-1/2 rounded-full bg-neutral-700/80 px-3 py-1 text-sm font-medium text-white shadow-lg shadow-black/20 backdrop-blur-sm transition-opacity duration-300 sm:bottom-8 sm:px-4 sm:py-1.5 ${controlsVisible ? "opacity-100" : "opacity-0"}`}
      >
        {currentIndex + 1} / {slides.length}
      </div>
    </div>
  );
}
