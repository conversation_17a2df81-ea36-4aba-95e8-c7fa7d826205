"use client";

import { ProjectRejectionHistoryDialog } from "@/components/projects/project-rejection-history-dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDialog } from "@/hooks/use-dialog";
import { PROJECT_STATUS } from "@/libs/constants";
import { useOrganizationMemberRole } from "@/queries/organization.queries";
import {
  useProjectById,
  useProjectDeleteMutation,
  useProjectUpdateMutation,
} from "@/queries/project.queries";
import type { ProjectStatus } from "@/types/project.types";
import {
  IconCancel,
  IconDots,
  IconPencil,
  IconPlayerPlay,
  IconRefresh,
  IconSend,
  IconTrash,
} from "@tabler/icons-react";
import Link from "next/link";

interface Props {
  disabled?: boolean;
  projectId: string;
  status: ProjectStatus;
  organizationSlug: string;
}

export function ProjectCardActionsMenu({
  disabled,
  projectId,
  organizationSlug,
  status,
}: Props) {
  const [openDeleteDialog, openDeleteDialogHandlers] = useDialog();
  const [openResubmitDialog, openResubmitDialogHandlers] = useDialog();

  const userRole = useOrganizationMemberRole();
  const project = useProjectById(projectId);

  const updateMutation = useProjectUpdateMutation();
  const deleteMutation = useProjectDeleteMutation();

  function onOpenDeleteDialog(e: React.SyntheticEvent) {
    e.stopPropagation();
    openDeleteDialogHandlers.open();
  }

  function onOpenResubmitDialog(e: React.SyntheticEvent) {
    e.stopPropagation();
    openResubmitDialogHandlers.open();
  }

  async function onStopProject() {
    await updateMutation.mutateAsync({
      id: projectId,
      status: "editing",
    });
  }

  async function onDelete() {
    await deleteMutation.mutateAsync({ id: projectId });
  }

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="size-7 text-gray-400 data-[state=open]:bg-accent data-[state=open]:text-gray-900"
            title="Open project actions menu"
          >
            <IconDots size={18} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <Link href={`/${organizationSlug}/projects/${projectId}`}>
            <DropdownMenuItem>
              <IconPencil className="size-4" />
              <span>Edit</span>
            </DropdownMenuItem>
          </Link>
          {status === PROJECT_STATUS.REJECTED && (
            <DropdownMenuItem
              onClick={onOpenResubmitDialog}
              disabled={disabled}
            >
              <IconRefresh className="size-4" />
              <span>View Rejection History</span>
            </DropdownMenuItem>
          )}
          {status === "editing" && userRole !== "admin" && (
            <Link
              href={`/${organizationSlug}/projects/${projectId}?action=publish`}
            >
              <DropdownMenuItem>
                <IconSend className="size-4" />
                <span>Submit for approval</span>
              </DropdownMenuItem>
            </Link>
          )}
          {status === "editing" && userRole === "admin" && (
            <Link
              href={`/${organizationSlug}/projects/${projectId}?action=publish`}
            >
              <DropdownMenuItem>
                <IconSend className="size-4" />
                <span>Publish</span>
              </DropdownMenuItem>
            </Link>
          )}
          <Link
            href={`/preview?projectId=${projectId}`}
            target="_blank"
            rel="noreferrer noopener"
          >
            <DropdownMenuItem>
              <IconPlayerPlay className="size-4" />
              <span>Preview</span>
            </DropdownMenuItem>
          </Link>

          {status === "active" && userRole === "admin" && (
            <DropdownMenuItem onClick={onStopProject} disabled={disabled}>
              <IconCancel className="size-4" />
              <span>Stop project</span>
            </DropdownMenuItem>
          )}

          <DropdownMenuItem
            className="text-red-500! hover:bg-red-500/5!"
            onClick={onOpenDeleteDialog}
            disabled={disabled}
          >
            <IconTrash className="size-4 text-red-500" />
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <DeleteDialog
        title="Project"
        open={openDeleteDialog}
        onClose={openDeleteDialogHandlers.close}
        onDelete={onDelete}
        loading={deleteMutation.isPending}
      />

      {project.data && (
        <ProjectRejectionHistoryDialog
          open={openResubmitDialog}
          onClose={openResubmitDialogHandlers.close}
          project={project.data}
        />
      )}
    </div>
  );
}
