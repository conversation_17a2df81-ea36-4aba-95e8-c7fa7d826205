"use client";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import { api } from "@/trpc/react";
import type { Project } from "@/types/project.types";
import { formatDate } from "@/utils/format-date";
import { IconX } from "@tabler/icons-react";

interface ProjectRejectionHistoryDialogProps extends DialogProps {
  onClose: () => void;
  project: Project;
}

export function ProjectRejectionHistoryDialog({
  open,
  onClose,
  project,
}: ProjectRejectionHistoryDialogProps) {
  const rejectionHistory = api.approvals.getRejectionHistory.useQuery(
    { projectId: project.id },
    { enabled: !!open },
  );

  const orgSlug = useOrganizationSlug();

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Rejection History</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <Alert variant="destructive">
            <IconX className="h-4 w-4" />
            <AlertTitle>Project Rejected</AlertTitle>
            <AlertDescription>
              This project was rejected during the approval process. Review the
              feedback below to understand why it was rejected.
            </AlertDescription>
          </Alert>

          <div className="space-y-3 rounded-lg border p-4">
            <h4 className="text-sm font-medium">Rejection History</h4>
            {rejectionHistory.isLoading && (
              <p className="text-sm text-muted-foreground">
                Loading history...
              </p>
            )}

            {rejectionHistory.data?.map((rejection, index) => (
              <div key={index} className="border-l-2 border-red-200 py-2 pl-4">
                <div className="mb-2 flex items-center gap-2">
                  <Badge variant="red">Rejected</Badge>
                  <span className="text-sm text-muted-foreground">
                    {formatDate(rejection.createdAt)}
                  </span>
                </div>
                {rejection.reviewComments && (
                  <p className="text-sm">{rejection.reviewComments}</p>
                )}
              </div>
            ))}

            {rejectionHistory.data && rejectionHistory.data.length === 0 && (
              <p className="text-sm text-muted-foreground">
                No rejection history found.
              </p>
            )}
          </div>

          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button href={`/${orgSlug}/projects/${project.id}`}>
              Resubmit for Approval
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
