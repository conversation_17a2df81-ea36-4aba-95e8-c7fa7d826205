"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import type { ApprovalReview } from "@/types/approvals.types";
import type { ProjectApprovalStatus } from "@/types/project.types";
import { formatDate } from "@/utils/format-date";
import { snakeToSpaces } from "@/utils/snake-case-to-spaces";
import {
  IconCalendarWeek,
  IconCheck,
  IconStack2,
  IconX,
} from "@tabler/icons-react";
import { useState } from "react";

interface ApprovalReviewDialogProps {
  approval: ApprovalReview;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onReview: (
    approvalId: string,
    status: ProjectApprovalStatus,
    comments?: string,
  ) => void;
}

export function ApprovalReviewDialog({
  approval,
  open,
  onOpenChange,
  onReview,
}: ApprovalReviewDialogProps) {
  const [reviewComments, setReviewComments] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleReview = async (status: ProjectApprovalStatus) => {
    if (status === "rejected" && !reviewComments) {
      alert("Please provide comments for rejection.");
      return;
    }
    setIsSubmitting(true);
    onReview(approval.id, status, reviewComments);
    setIsSubmitting(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="pb-3 sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Review Approval Request</DialogTitle>
          <DialogDescription>
            Review the project approval request and provide your decision.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Project Info */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">{approval.project.name}</h3>
              <div className="mt-2 flex items-center gap-2 text-sm text-gray-600">
                <Badge variant="gray" className="flex items-center gap-1.5">
                  <IconStack2 className="h-3.5 w-3.5" />
                  <span>{approval.project._count.slides} slides</span>
                </Badge>
                <Badge variant="yellow" className="capitalize">
                  {snakeToSpaces(approval.project.status)}
                </Badge>
              </div>
              <div className="mt-3 flex items-center gap-2 text-sm text-gray-600">
                <Badge variant="gray" className="flex items-center gap-1.5">
                  <IconCalendarWeek className="h-3.5 w-3.5" />
                  <span>Start: {formatDate(approval.project.startDate)}</span>
                </Badge>
                <Badge variant="gray" className="flex items-center gap-1.5">
                  <IconCalendarWeek className="h-3.5 w-3.5" />
                  <span>End: {formatDate(approval.project.endDate)}</span>
                </Badge>
              </div>
            </div>

            {/* Requester Info */}
            <div className="flex items-center gap-3 rounded-lg border bg-gray-50 p-3">
              <Avatar>
                <AvatarImage src={approval.requester.image || ""} />
                <AvatarFallback>
                  {approval.requester.name?.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">{approval.requester.name}</p>
                <p className="text-sm text-gray-600">
                  Submitted {formatDate(approval.submittedAt)}
                </p>
              </div>
            </div>

            {/* Request Comments */}
            {approval.comments && (
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Request Comments
                </label>
                <div className="mt-[5px] rounded-lg border bg-gray-50 p-3">
                  <p className="text-sm text-gray-700">{approval.comments}</p>
                </div>
              </div>
            )}
          </div>

          {/* Review Comments */}
          <div className="space-y-2">
            <Textarea
              label="Review Comments (Optional)"
              placeholder="Add comments about your decision..."
              value={reviewComments}
              onChange={(e) => setReviewComments(e.target.value)}
              rows={4}
            />
          </div>
        </div>

        <DialogFooter className="mt-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <div className="flex gap-2">
            <Button
              variant="destructive"
              onClick={() => handleReview("rejected")}
              leftIcon={<IconX className="h-4 w-4" />}
              disabled={isSubmitting}
            >
              Reject
            </Button>
            <Button
              onClick={() => handleReview("approved")}
              leftIcon={<IconCheck className="h-4 w-4" />}
              disabled={isSubmitting}
            >
              Approve
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
