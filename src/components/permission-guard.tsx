import { usePermissions } from "@/hooks/use-permissions";
import type { CapabilitySpec } from "@/libs/auth-permissions";
import type { Role } from "@/types/organization.types";
import { ReactNode, useMemo } from "react";

interface PermissionGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
  allowedRoles?: Role[]; // legacy role gating
  resource?: string; // shorthand resource
  actions?: string[]; // shorthand actions for the resource
  spec?: CapabilitySpec; // full spec (AND of actions per resource)
  anyOf?: CapabilitySpec[]; // OR across specs
  allOf?: CapabilitySpec[]; // AND across specs
}

/**
 * PermissionGuard
 * ---------------------------------------------------------------------------
 * Backward-compatible component for conditionally rendering children based on:
 *   1. Allowed roles (legacy) AND/OR
 *   2. Fine‑grained capability specifications
 *
 * If both role and capability props are supplied the user must satisfy BOTH.
 * If neither is supplied the children render unconditionally.
 *
 * Capability Props (all optional):
 * - resource + actions: shorthand (e.g. resource="project" actions={["publish"]})
 * - spec: full CapabilitySpec object { project: ["publish"], location: ["assign"] }
 * - anyOf: array of CapabilitySpecs; passes if ANY spec in the array matches.
 * - allOf: array of CapabilitySpecs; passes only if ALL specs match.
 *
 * Evaluation order:
 *   1. Build spec(s) from props
 *   2. Evaluate role condition (if provided)
 *   3. Evaluate capability conditions
 *   4. Render children if all satisfied else fallback
 */
export function PermissionGuard(props: PermissionGuardProps) {
  const {
    children,
    fallback = null,
    allowedRoles = [],
    resource,
    actions,
    spec,
    anyOf,
    allOf,
  } = props;

  const { can, role: userRole } = usePermissions();

  // Build a primary spec from shorthand (resource/actions) if provided
  const shorthandSpec: CapabilitySpec | undefined = useMemo(() => {
    if (!resource) return undefined;
    return { [resource]: actions && actions.length ? actions : ["read"] };
  }, [resource, actions]);

  const roleAllowed = useMemo(() => {
    if (allowedRoles.length === 0) return true;
    return allowedRoles.includes(userRole);
  }, [allowedRoles, userRole]);

  const capabilityAllowed = useMemo(() => {
    // If no capability props provided treat as pass-through
    if (!spec && !shorthandSpec && !anyOf && !allOf) return true;

    // Evaluate spec (AND semantics per resource/actions)
    if (spec && !can(spec)) return false;
    if (shorthandSpec && !can(shorthandSpec)) return false;

    if (allOf && allOf.length) {
      const allOk = allOf.every((s) => can(s));
      if (!allOk) return false;
    }
    if (anyOf && anyOf.length) {
      const anyOk = anyOf.some((s) => can(s));
      if (!anyOk) return false;
    }
    return true;
  }, [spec, shorthandSpec, anyOf, allOf, can]);

  if (roleAllowed && capabilityAllowed) return <>{children}</>;
  return <>{fallback}</>;
}

export default PermissionGuard;
