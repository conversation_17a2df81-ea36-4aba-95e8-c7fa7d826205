"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useUpdateOrgRoleMutation } from "@/queries/organization.queries";
import { Roles } from "@/types/organization.types";
import { useEffect, useState } from "react";
import { PermissionMap, PermissionSelector } from "./permission-selector";

const isBuiltIn = (name: string) =>
  name === Roles.ADMIN || name === Roles.MANAGER || name === Roles.MEMBER;

interface RoleEditDialogProps extends DialogProps {
  onClose: () => void;
  orgId: string;
  initialName: string;
  initialPermission: PermissionMap;
  isBuiltInRole?: boolean;
}

export function RoleEditD<PERSON>og({
  open,
  onClose,
  orgId,
  initialName,
  initialPermission,
  isBuiltInRole,
}: RoleEditDialogProps) {
  const [name, setName] = useState(initialName);
  const [permissions, setPermissions] = useState<PermissionMap>(initialPermission);
  const [isInitialized, setIsInitialized] = useState(false);

  const updateRole = useUpdateOrgRoleMutation();

  useEffect(() => {
    if (open && !isInitialized) {
      setName(initialName);
      setPermissions(initialPermission || {});
      setIsInitialized(true);
    } else if (!open && isInitialized) {
      setIsInitialized(false);
    }
  }, [open, initialName, initialPermission, isInitialized]);

  const closeModal = () => {
    setName("");
    setPermissions({});
    setIsInitialized(false);
    onClose();
  };

  const toNonEmptyPermission = (
    p: PermissionMap,
  ): Record<string, [string, ...string[]]> =>
    Object.fromEntries(
      Object.entries(p).map(([k, v]) => [k, v as [string, ...string[]]]),
    ) as Record<string, [string, ...string[]]>;

  const handleSubmit = async () => {
    if (!name.trim()) return;

    try {
      await updateRole.mutateAsync({
        orgId,
        roleName: initialName,
        data: {
          ...(name !== initialName ? { roleName: name } : {}),
          permission: toNonEmptyPermission(permissions),
        },
      });
      closeModal();
    } catch (error) {
      // Error handling is done by the mutation (toast notifications)
      console.error("Error updating role:", error);
    }
  };

  const isLoading = updateRole.isPending;

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Edit Role</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label>Role name</Label>
            <Input
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="e.g. publisher"
              disabled={isLoading || (isBuiltInRole || isBuiltIn(initialName))}
            />
          </div>

          <Separator />

          <PermissionSelector
            permissions={permissions}
            onPermissionChange={setPermissions}
            roleName={name}
            isBuiltInRole={isBuiltInRole || isBuiltIn(initialName)}
            disabled={isLoading}
          />
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={closeModal}
            type="button"
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button onClick={handleSubmit} loading={isLoading}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
