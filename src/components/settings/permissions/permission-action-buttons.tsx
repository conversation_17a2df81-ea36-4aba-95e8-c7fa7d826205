"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useUpdatePermissionOverridesMutation } from "@/queries/organization.queries";
import { Roles } from "@/types/organization.types";
import {
  IconChevronDown,
  IconRefresh,
  IconRocket,
  IconSettings,
} from "@tabler/icons-react";
import { toast } from "sonner";

interface Props {
  organizationId?: string;
}

export function PermissionActionButtons({ organizationId }: Props) {
  const updatePermissionsMutation = useUpdatePermissionOverridesMutation();

  const handleQuickAction = async (
    action: "grantManagerPublish" | "grantMemberBasics" | "resetToDefaults",
  ) => {
    if (!organizationId) return;

    try {
      switch (action) {
        case "grantManagerPublish":
          await updatePermissionsMutation.mutateAsync({
            orgId: organizationId,
            role: Roles.MANAGER,
            permissions: {
              project: ["publish"],
            },
          });
          break;

        case "grantMemberBasics":
          await updatePermissionsMutation.mutateAsync({
            orgId: organizationId,
            role: Roles.MEMBER,
            permissions: {
              project: ["assign_locations"],
              slide: ["mark_important"],
            },
          });
          break;

        case "resetToDefaults":
          // TODO: Implement reset functionality
          // For now, this would require a separate API endpoint to clear overrides
          toast.info("Reset to defaults is not yet implemented");
          break;
      }
    } catch (error) {
      console.error("Failed to execute quick action:", error);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          disabled={updatePermissionsMutation.isPending || !organizationId}
          className="gap-2"
          leftIcon={<IconSettings className="size-4" />}
          rightIcon={<IconChevronDown className="size-3" />}
        >
          Quick Actions
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Common Permission Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={() => handleQuickAction("grantManagerPublish")}
          disabled={updatePermissionsMutation.isPending}
          className="gap-2"
        >
          <IconRocket className="size-4" />
          Grant Publish to Managers
        </DropdownMenuItem>

        <DropdownMenuItem
          onClick={() => handleQuickAction("grantMemberBasics")}
          disabled={updatePermissionsMutation.isPending}
          className="gap-2"
        >
          <IconSettings className="size-4" />
          Grant Basic Permissions to Members
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={() => handleQuickAction("resetToDefaults")}
          disabled={updatePermissionsMutation.isPending}
          className="gap-2 text-red-600"
        >
          <IconRefresh className="size-4" />
          Reset to Defaults
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
