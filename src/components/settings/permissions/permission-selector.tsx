"use client";

import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { getStatements } from "@/libs/auth-permissions";
import { cn } from "@/libs/utils";
import {
  IconBuilding,
  IconFolder,
  IconLocation,
  IconUsers,
} from "@tabler/icons-react";
import { useMemo } from "react";

export type PermissionMap = Record<string, string[]>;

interface PermissionSelectorProps {
  permissions: PermissionMap;
  onPermissionChange: (permissions: PermissionMap) => void;
  roleName: string;
  isBuiltInRole?: boolean;
  disabled?: boolean;
}

export function PermissionSelector({
  permissions,
  onPermissionChange,
  roleName: _roleName,
  isBuiltInRole,
  disabled = false,
}: PermissionSelectorProps) {
  const statements = useMemo(() => getStatements(), []);

  // Filter to only show organization member-relevant resources
  // This ensures the selector only displays permissions that are relevant to organization members
  // rather than showing all system resources like slides, approvals, etc.
  const memberRelevantResources = [
    "project",
    "location",
    "organization",
    "member",
  ];

  const filteredStatements = useMemo(() => {
    return Object.fromEntries(
      Object.entries(statements).filter(([resource]) =>
        memberRelevantResources.includes(resource),
      ),
    );
  }, [statements]);

  const toggleAction = (resource: string, action: string, checked: boolean) => {
    const current = new Set(permissions[resource] || []);

    if (checked) {
      current.add(action);
    } else {
      current.delete(action);
    }

    const updatedPermissions = { ...permissions };
    if (current.size > 0) {
      updatedPermissions[resource] = Array.from(current);
    } else {
      delete updatedPermissions[resource];
    }

    onPermissionChange(updatedPermissions);
  };

  return (
    <div className={cn("space-y-4", disabled && "opacity-80")}>
      {Object.entries(filteredStatements).map(([resource, actions]) => {
        const selectedCount = new Set(permissions[resource] || []).size;

        const Icon =
          resource === "organization"
            ? IconBuilding
            : resource === "member"
              ? IconUsers
              : resource === "location"
                ? IconLocation
                : resource === "project"
                  ? IconFolder
                  : undefined;

        return (
          <section
            key={resource}
            className="shadow-xxs rounded-lg border border-border/60 bg-card/60 p-4 transition-colors hover:bg-card"
          >
            <div className="mb-3 flex items-center justify-between">
              <div className="flex items-center gap-2">
                {Icon && <Icon size={16} stroke={1.8} />}
                <div className="text-sm font-medium tracking-tight capitalize">
                  {resource}
                </div>
              </div>
              <Badge variant={selectedCount > 0 ? "blue" : "gray"}>
                {selectedCount} selected
              </Badge>
            </div>

            <div className="flex flex-wrap gap-2 sm:gap-3">
              {Array.from(actions).map((a) => {
                const id = `${resource}-${a}`;
                const checked = new Set(permissions[resource] || []).has(a);
                // For built-in roles, we consider all permissions as base permissions
                const baseChecked = isBuiltInRole;
                const isDisabled = disabled || (isBuiltInRole && baseChecked);

                return (
                  <label
                    key={id}
                    htmlFor={id}
                    className={cn(
                      "group inline-flex items-center gap-2 rounded-full border px-3 py-1.5 text-xs sm:text-sm",
                      "cursor-pointer transition-colors",
                      checked
                        ? "border-primary/40 bg-primary/5 text-foreground"
                        : "border-border/70 bg-background text-muted-foreground hover:bg-accent/40",
                      isDisabled && "cursor-not-allowed opacity-70",
                    )}
                  >
                    <Checkbox
                      id={id}
                      checked={checked}
                      disabled={isDisabled}
                      onCheckedChange={(c) => toggleAction(resource, a, !!c)}
                      className={cn(
                        "data-[state=checked]:border-primary data-[state=checked]:bg-primary",
                        "h-4 w-4",
                      )}
                    />
                    <span className="whitespace-nowrap">
                      {a}
                      {baseChecked && (
                        <Badge variant="blue" className="ml-2 align-middle">
                          Base
                        </Badge>
                      )}
                    </span>
                  </label>
                );
              })}
            </div>
          </section>
        );
      })}
    </div>
  );
}
