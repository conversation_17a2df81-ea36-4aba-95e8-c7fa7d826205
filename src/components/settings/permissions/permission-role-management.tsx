"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import { useDialog } from "@/hooks/use-dialog";
import {
  useDeleteOrgRoleMutation,
  useOrgRoles,
} from "@/queries/organization.queries";
import { PermissionMap } from "./permission-selector";
import { RoleCreateDialog } from "./role-create-dialog";
import { RoleEditDialog } from "./role-edit-dialog";

import { EmptyState } from "@/components/ui/empty-state";
import { Roles } from "@/types/organization.types";
import { IconEdit, IconPlus, IconShield, IconTrash } from "@tabler/icons-react";
import { useState } from "react";

interface Props {
  orgId?: string;
}

const isBuiltIn = (name: string) =>
  name === Roles.ADMIN || name === Roles.MANAGER || name === Roles.MEMBER;

export function PermissionRoleManagement({ orgId }: Props) {
  const rolesQuery = useOrgRoles(orgId || "");

  const deleteRole = useDeleteOrgRoleMutation();

  // Get the list of organization member-relevant resources to filter permissions display
  const memberRelevantResources = [
    "project",
    "location",
    "organization",
    "member",
  ];

  const [openDeleteDialog, deleteDialogHandlers] = useDialog();
  const [roleToDelete, setRoleToDelete] = useState<string | null>(null);

  const roles: Array<{
    roleName?: string;
    role?: string;
    permission?: PermissionMap;
  }> = (rolesQuery.data ?? []).map((role: any) => ({
    ...role,
    permission: role.permission || {},
  }));

  const [createDialogOpen, createDialogHandlers] = useDialog();
  const [editDialogOpen, editDialogHandlers] = useDialog();
  const [editing, setEditing] = useState<null | {
    name: string;
    permission: PermissionMap;
  }>(null);

  const openCreate = () => {
    createDialogHandlers.open();
  };

  const openEdit = (name: string, permission: PermissionMap = {}) => {
    setEditing({ name, permission });
    editDialogHandlers.open();
  };

  const onRequestDelete = (roleName: string) => {
    if (isBuiltIn(roleName)) return;
    setRoleToDelete(roleName);
    deleteDialogHandlers.open();
  };

  const onConfirmDelete = async () => {
    if (!orgId || !roleToDelete) return;
    await deleteRole.mutateAsync({ orgId, roleName: roleToDelete });
    setRoleToDelete(null);
    deleteDialogHandlers.close();
  };

  return (
    <Card>
      <div className="flex items-center justify-between border-b border-gray-200 bg-sidebar px-6 py-4">
        <p className="text-base leading-6 font-medium">Roles</p>
        <Button
          leftIcon={<IconPlus className="h-4 w-4" />}
          onClick={openCreate}
          disabled={!orgId}
          variant={"outline"}
        >
          Create Role
        </Button>
      </div>

      <div className="max-h-96 overflow-y-auto">
        {roles.length === 0 ? (
          <div className="p-20">
            <div className="mx-auto w-full lg:max-w-2xl">
              <EmptyState
                title="No custom roles yet"
                subtitle="Create custom roles to define specific permissions for your organization members."
                icon={<IconShield size={50} className="text-dark-500" />}
                actionButton={
                  <Button
                    leftIcon={<IconPlus size={16} />}
                    onClick={openCreate}
                    disabled={!orgId}
                    variant="outline"
                  >
                    Create Role
                  </Button>
                }
              />
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {(roles || []).map((r: any) => {
              const roleName = r.roleName ?? r.role ?? "";
              const permission: PermissionMap = r.permission ?? {};
              const isCore = isBuiltIn(roleName);
              return (
                <div
                  key={roleName}
                  className="flex items-center justify-between px-6 py-4"
                >
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-semibold md:text-base">
                        {roleName}
                      </span>
                      {isCore && (
                        <Badge
                          variant="outline"
                          className="border-blue-200 bg-blue-50 text-xs text-blue-700"
                        >
                          Built-in
                        </Badge>
                      )}
                    </div>
                    <div className="mt-1 flex flex-wrap gap-1 text-sm">
                      {/* Only show organization member-relevant permissions */}
                      {Object.entries(permission).filter(([res]) =>
                        memberRelevantResources.includes(res),
                      ).length === 0 && (
                        <span className="text-gray-600">
                          No additive permissions
                        </span>
                      )}
                      {Object.entries(permission)
                        .filter(([res]) =>
                          memberRelevantResources.includes(res),
                        )
                        .flatMap(([res, acts]) => {
                          // Ensure acts is an array before calling map
                          const actionsArray = Array.isArray(acts) ? acts : [];
                          return actionsArray.map((a) => (
                            <Badge
                              key={`${roleName}-${res}-${a}`}
                              variant="secondary"
                              className="text-xs"
                            >
                              {res}:{a}
                            </Badge>
                          ));
                        })}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEdit(roleName, permission)}
                      leftIcon={<IconEdit className="h-4 w-4" />}
                      className="hidden md:flex"
                    >
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={isCore}
                      onClick={() => onRequestDelete(roleName)}
                      leftIcon={<IconTrash className="h-4 w-4" />}
                      className="hidden md:flex"
                    >
                      Delete
                    </Button>
                    {/* Mobile action buttons */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEdit(roleName, permission)}
                      className="md:hidden"
                    >
                      <IconEdit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={isCore}
                      onClick={() => onRequestDelete(roleName)}
                      className="md:hidden"
                    >
                      <IconTrash className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      <RoleCreateDialog
        open={createDialogOpen}
        onClose={createDialogHandlers.close}
        orgId={orgId || ""}
      />

      {editing && (
        <RoleEditDialog
          open={editDialogOpen}
          onClose={() => {
            editDialogHandlers.close();
            setEditing(null);
          }}
          orgId={orgId || ""}
          initialName={editing.name}
          initialPermission={editing.permission}
          isBuiltInRole={isBuiltIn(editing.name)}
        />
      )}

      <DeleteDialog
        title="Role"
        open={openDeleteDialog}
        onClose={() => {
          deleteDialogHandlers.close();
          setRoleToDelete(null);
        }}
        onDelete={onConfirmDelete}
        loading={deleteRole.isPending}
      />
    </Card>
  );
}
