"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useCreateOrgRoleMutation } from "@/queries/organization.queries";
import { useState } from "react";
import { PermissionMap, PermissionSelector } from "./permission-selector";

interface RoleCreateDialogProps extends DialogProps {
  onClose: () => void;
  orgId: string;
}

export function RoleCreateDialog({
  open,
  onClose,
  orgId,
}: RoleCreateDialogProps) {
  const [name, setName] = useState("");
  const [permissions, setPermissions] = useState<PermissionMap>({});

  const createRole = useCreateOrgRoleMutation(orgId);

  const closeModal = () => {
    setName("");
    setPermissions({});
    onClose();
  };

  const toNonEmptyPermission = (
    p: PermissionMap,
  ): Record<string, [string, ...string[]]> =>
    Object.fromEntries(
      Object.entries(p).map(([k, v]) => [k, v as [string, ...string[]]]),
    ) as Record<string, [string, ...string[]]>;

  const handleSubmit = async () => {
    if (!name.trim()) return;

    try {
      await createRole.mutateAsync({
        orgId,
        role: name,
        permission: toNonEmptyPermission(permissions),
      });
      closeModal();
    } catch (error) {
      // Error handling is done by the mutation (toast notifications)
      console.error("Error creating role:", error);
    }
  };

  const isLoading = createRole.isPending;

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Create a new role</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Input
              label="Role name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="e.g. publisher"
              disabled={isLoading}
            />
          </div>

          <Separator />

          <PermissionSelector
            permissions={permissions}
            onPermissionChange={setPermissions}
            roleName={name}
            disabled={isLoading}
          />
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={closeModal}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} loading={isLoading}>
            Create Role
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
