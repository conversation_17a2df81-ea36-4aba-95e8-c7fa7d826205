"use client";

import { PermissionGuard } from "@/components/permission-guard";
import { PermissionActionButtons } from "@/components/settings/permissions/permission-action-buttons";
import { PermissionMatrix } from "@/components/settings/permissions/permission-matrix";
import { PermissionRoleManagement } from "@/components/settings/permissions/permission-role-management";
import { SettingsSection } from "@/components/settings/settings-section";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card } from "@/components/ui/card";
import { Loader } from "@/components/ui/loader";
import { useOrganizationBySlug } from "@/queries/organization.queries";
import { Roles } from "@/types/organization.types";
import { IconInfoCircle, IconShieldCheck } from "@tabler/icons-react";

interface Props {
  slug: string;
}

export function SettingsPermissionsView({ slug }: Props) {
  const organizationQuery = useOrganizationBySlug(slug);

  if (organizationQuery.isLoading) {
    return (
      <div className="flex items-center justify-center py-[300px]">
        <Loader />
      </div>
    );
  }

  return (
    <PermissionGuard
      allowedRoles={[Roles.ADMIN]}
      fallback={
        <SettingsSection>
          <div>
            <h2 className="text-base leading-7 font-semibold">
              Permission Management
            </h2>
            <p className="mt-1 text-[15px] leading-6 text-gray-500">
              Configure role-based permissions for your organization.
            </p>
          </div>

          <Card className="md:col-span-2">
            <div className="p-6">
              <Alert>
                <IconShieldCheck className="h-4 w-4" />
                <AlertDescription>
                  You need administrator privileges to manage permissions.
                </AlertDescription>
              </Alert>
            </div>
          </Card>
        </SettingsSection>
      }
    >
      <SettingsSection>
        <div>
          <h2 className="text-base leading-7 font-semibold">
            Permission Management
          </h2>
          <p className="mt-1 text-[15px] leading-6 text-gray-500">
            Configure role-based permissions for your organization. Permissions
            are additive - you can grant additional capabilities to roles beyond
            their default settings.
          </p>
        </div>

        <div className="space-y-6 md:col-span-2">
          <Alert>
            <IconInfoCircle className="h-4 w-4" />
            <AlertDescription>
              Changes are saved automatically. Granted permissions are additive
              - they extend the base role capabilities without removing existing
              ones.
            </AlertDescription>
          </Alert>

          <PermissionRoleManagement orgId={organizationQuery.data?.id} />

          <Card>
            <div className="flex items-center justify-between border-b border-gray-200 bg-sidebar px-6 py-4">
              <p className="text-base leading-6 font-medium">
                Role Permissions
              </p>
              <PermissionActionButtons
                organizationId={organizationQuery.data?.id}
              />
            </div>

            <div className="p-6">
              <PermissionMatrix organizationId={organizationQuery.data?.id} />
            </div>
          </Card>
        </div>
      </SettingsSection>
    </PermissionGuard>
  );
}
