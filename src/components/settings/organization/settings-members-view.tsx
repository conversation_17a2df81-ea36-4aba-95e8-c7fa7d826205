"use client";

import { OrganizationMemberActionsMenu } from "@/components/organizations/organization-member-actions-menu";
import { UserInviteDialog } from "@/components/settings/organization/user-invite-dialog";
import { SettingsSection } from "@/components/settings/settings-section";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import { EmptyState } from "@/components/ui/empty-state";
import { Loader } from "@/components/ui/loader";
import { Separator } from "@/components/ui/separator";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useDialog } from "@/hooks/use-dialog";
import { dayjs } from "@/libs/dayjs";
import { cn } from "@/libs/utils";
import {
  useCreateOrganizationInviteMutation,
  useOrganizationBySlug,
  useOrganizationDeleteInviteMutation,
  useOrganizationInvites,
  useOrganizationMemberDeleteMutation,
  useOrganizationMemberRole,
  useOrganizationMembers,
} from "@/queries/organization.queries";
import { useUser } from "@/queries/user.queries";
import {
  Roles,
  type OrganizationInvite,
  type OrganizationMember,
  type Role,
} from "@/types/organization.types";
import { getInitials } from "@/utils/get-initials";
import { getMemberRoleBadgeVariant } from "@/utils/get-member-role-badge-variant";
import { isEmpty } from "@/utils/is-empty";
import { IconMailForward, IconPlus, IconTrash } from "@tabler/icons-react";
import { useRouter } from "next/navigation";

export function SettingsMembersView({ slug }: { slug: string }) {
  const [inviteModal, inviteModalHandler] = useDialog();

  const organization = useOrganizationBySlug(slug);
  const members = useOrganizationMembers(organization.data?.id ?? "");
  const invitations = useOrganizationInvites(organization.data?.id ?? "");
  const userMemberRole = useOrganizationMemberRole();

  const sendInvitationMutation = useCreateOrganizationInviteMutation();

  const handleSendInvitation = async (data: { email: string; role: Role }) => {
    await sendInvitationMutation.mutateAsync({
      ...data,
      organizationId: organization.data?.id ?? "",
    });
  };

  const isManagerOrViewer =
    userMemberRole === Roles.MANAGER || userMemberRole === Roles.MEMBER;

  return (
    <div>
      {organization.isLoading && (
        <div className="flex items-center justify-center py-[300px]">
          <Loader />
        </div>
      )}

      {!organization.isLoading && (
        <div>
          <SettingsSection>
            <div>
              <h2 className="text-base leading-7 font-semibold">
                Organization Team
              </h2>
              <p className="mt-1 text-[15px] leading-6 text-gray-500">
                Manage your team members and their access levels.
              </p>
            </div>

            <Card className="md:col-span-2">
              <div className="flex items-center justify-between border-b border-gray-200 bg-sidebar px-6 py-4">
                <p className="text-base leading-6 font-medium">Team members</p>
              </div>

              <div className="max-h-96 overflow-y-auto">
                {members.isLoading && (
                  <div className="flex items-center justify-center p-6">
                    <Loader />
                  </div>
                )}
                {!members.isLoading && (
                  <div className="divide-y divide-gray-200">
                    {/* There will always be at least one member here as the creator gets added upon organization creation */}
                    {members?.data?.map((member, index) => (
                      <MemberCard
                        member={member}
                        index={index}
                        key={member?.id}
                        userMemberRole={userMemberRole}
                      />
                    ))}
                  </div>
                )}
              </div>
            </Card>
          </SettingsSection>

          <Separator className="my-8" />

          <SettingsSection>
            <div>
              <h2 className="text-base leading-7 font-semibold">
                Pending Invitations
              </h2>
              <p className="mt-1 text-[15px] leading-6 text-gray-500">
                Manage your pending invitations.
              </p>
            </div>

            <Card className="md:col-span-2">
              <div className="flex items-center justify-between border-b border-gray-200 bg-sidebar px-6 py-4">
                <p className="text-base leading-6 font-medium">Invitations</p>
                <div>
                  <>
                    {isManagerOrViewer && (
                      <TooltipProvider delayDuration={150}>
                        <Tooltip>
                          <TooltipTrigger className="cursor-not-allowed">
                            <Button
                              variant="outline"
                              leftIcon={<IconPlus size={16} />}
                              disabled
                            >
                              Invite member
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              Only members with the Admin role can invite
                              members.
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}

                    {!isManagerOrViewer && (
                      <Button
                        variant="outline"
                        leftIcon={<IconPlus size={16} />}
                        onClick={inviteModalHandler.open}
                      >
                        Invite member
                      </Button>
                    )}
                  </>
                </div>
              </div>

              <div className="max-h-96 overflow-y-auto">
                {invitations?.isLoading && (
                  <div className="flex items-center justify-center p-6">
                    <Loader />
                  </div>
                )}

                {!invitations.isLoading && (
                  <div className="divide-y divide-gray-200">
                    {invitations?.data?.map((invite) => (
                      <InviteCard invite={invite} key={invite.id} />
                    ))}
                    {isEmpty(invitations?.data) && (
                      <div className="p-20">
                        <div className="mx-auto w-full lg:max-w-2xl">
                          <EmptyState
                            title="You dont have any invites yet"
                            subtitle="Invite a teammate to collaborate on this organization with."
                            icon={
                              <IconMailForward
                                size={50}
                                className="text-dark-500"
                              />
                            }
                            actionButton={
                              <>
                                {isManagerOrViewer && (
                                  <TooltipProvider delayDuration={150}>
                                    <Tooltip>
                                      <TooltipTrigger className="cursor-not-allowed">
                                        <Button
                                          variant="outline"
                                          leftIcon={<IconPlus size={16} />}
                                          disabled
                                        >
                                          Invite member
                                        </Button>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>
                                          Only members with the Admin role can
                                          invite members.
                                        </p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                )}

                                {!isManagerOrViewer && (
                                  <Button
                                    variant="outline"
                                    leftIcon={<IconPlus size={16} />}
                                    onClick={inviteModalHandler.open}
                                  >
                                    Invite member
                                  </Button>
                                )}
                              </>
                            }
                          />
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </Card>
          </SettingsSection>
        </div>
      )}

      <UserInviteDialog
        open={inviteModal}
        onClose={inviteModalHandler.close}
        onInvite={handleSendInvitation}
      />
    </div>
  );
}

interface MemberProps {
  member: OrganizationMember;
  index: number;
  userMemberRole?: string;
}

function MemberCard({ member, userMemberRole }: MemberProps) {
  const user = useUser();
  const router = useRouter();
  const [deleteModal, deleteModalHandler] = useDialog();

  const deleteMutation = useOrganizationMemberDeleteMutation(
    member?.organizationId,
  );

  const handleDelete = async () => {
    await deleteMutation.mutateAsync({ memberId: member.id });
    if (member.user.id === user?.data?.id) {
      router.push("/organizations");
    }
  };

  return (
    <div
      key={member?.id}
      className={cn("flex items-center justify-between px-6 py-4")}
    >
      <div>
        <div className="flex items-center space-x-2 md:space-x-4">
          <Avatar className="h-7 w-7 rounded-lg md:h-10 md:w-10">
            <AvatarImage src={member?.user?.image || ""} />
            <AvatarFallback className="text-white uppercase">
              {getInitials(member?.user?.email, 1)}
            </AvatarFallback>
          </Avatar>

          <div>
            <p className="text-sm font-semibold md:text-base">
              {member?.user?.name}
            </p>
            <p className="text-sm text-gray-600">{member?.user?.email}</p>
          </div>
        </div>

        <Badge
          variant={getMemberRoleBadgeVariant(member.role)}
          className="mt-4 capitalize md:hidden"
        >
          {member.role}
        </Badge>
      </div>

      <div className="flex items-center space-x-5">
        <p className="hidden text-gray-600 md:inline-block">
          Joined {dayjs(member.createdAt).fromNow()}
        </p>
        <Badge
          variant={getMemberRoleBadgeVariant(member.role)}
          className="hidden capitalize md:inline-block"
        >
          {member.role}
        </Badge>
        <OrganizationMemberActionsMenu
          member={member}
          userMemberRole={userMemberRole}
        />
      </div>

      <DeleteDialog
        title="member"
        open={deleteModal}
        onClose={deleteModalHandler.close}
        onDelete={handleDelete}
        loading={deleteMutation.isPending}
      />
    </div>
  );
}

interface InviteProps {
  invite: OrganizationInvite;
}

function InviteCard({ invite }: InviteProps) {
  const [inviteDeleteModal, inviteDeleteModalHandler] = useDialog();

  const deleteInviteMutation = useOrganizationDeleteInviteMutation();

  const deleteInvite = async (id: string) => {
    await deleteInviteMutation.mutateAsync({ id });
  };

  return (
    <div>
      <div className="flex w-full items-center justify-between px-6 py-4">
        <div className="flex items-center space-x-2 md:space-x-4">
          <Avatar className="h-7 w-7 md:h-10 md:w-10">
            <AvatarFallback>{getInitials(invite?.email, 1)}</AvatarFallback>
          </Avatar>

          <div>
            <p className="truncate text-sm text-gray-600 md:text-base">
              {invite?.email}
            </p>
          </div>
        </div>

        <div className="flex items-center md:space-x-3">
          <p className="hidden text-gray-600 md:inline-block">
            Invited {dayjs(invite.createdAt).fromNow()}
          </p>
          <Button
            variant="outline"
            size="icon"
            className="h-7 w-7"
            onClick={inviteDeleteModalHandler.open}
          >
            <IconTrash size={16} className="text-red-500" />
          </Button>
        </div>
      </div>

      <DeleteDialog
        title="Invitation"
        open={inviteDeleteModal}
        onClose={inviteDeleteModalHandler.close}
        onDelete={() => deleteInvite(invite.id)}
        loading={deleteInviteMutation.isPending}
      />
    </div>
  );
}
