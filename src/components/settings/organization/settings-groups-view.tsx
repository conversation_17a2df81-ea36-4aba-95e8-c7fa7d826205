"use client";

import { GroupActionsMenu } from "@/components/groups/group-actions-menu";
import { CreateGroupDialog } from "@/components/groups/group-create-dialog";
import { EditGroupDialog } from "@/components/groups/group-edit-dialog";
import { ManageGroupMembersDialog } from "@/components/groups/group-manage-members-dialog";
import { PermissionGuard } from "@/components/permission-guard";
import { SettingsSection } from "@/components/settings/settings-section";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { EmptyState } from "@/components/ui/empty-state";
import { Input } from "@/components/ui/input";
import { Loader } from "@/components/ui/loader";
import { useDialog } from "@/hooks/use-dialog";
import { useGroups } from "@/queries/groups.queries";
import { useOrganizationBySlug } from "@/queries/organization.queries";
import type { GroupOutput } from "@/types/group.types";
import { formatDate } from "@/utils/format-date";
import { IconPlus, IconSearch, IconUsers } from "@tabler/icons-react";
import { useMemo, useState, type KeyboardEvent } from "react";

export function SettingsGroupsView({ slug }: { slug: string }) {
  const [searchQuery, setSearchQuery] = useState("");
  const [createDialog, createDialogHandler] = useDialog();
  const [editDialog, editDialogHandler] = useDialog();
  const [manageMembersDialog, manageMembersDialogHandler] = useDialog();
  const [selectedGroup, setSelectedGroup] = useState<GroupOutput | null>(null);

  const organization = useOrganizationBySlug(slug);
  const { data: groups, isLoading } = useGroups({
    organizationId: organization.data?.id ?? "",
  });

  // Filter groups based on search query
  const filteredGroups = useMemo(() => {
    if (!groups?.data) return [];

    if (!searchQuery) return groups.data;

    return groups.data.filter((group) =>
      group.name.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [groups, searchQuery]);

  const handleEditGroup = (group: GroupOutput) => {
    setSelectedGroup(group);
    editDialogHandler.open();
  };

  const handleManageMembers = (group: GroupOutput) => {
    setSelectedGroup(group);
    manageMembersDialogHandler.open();
  };

  const handleCloseEditDialog = () => {
    editDialogHandler.close();
    setSelectedGroup(null);
  };

  const handleCloseMembersDialog = () => {
    manageMembersDialogHandler.close();
    setSelectedGroup(null);
  };

  return (
    <div>
      <SettingsSection>
        <div>
          <h2 className="text-base leading-7 font-semibold">Groups</h2>
          <p className="mt-1 text-[15px] leading-6 text-gray-500">
            Organize members into groups to manage access and settings.
          </p>
        </div>

        <Card className="md:col-span-2">
          <div className="flex items-center justify-between border-b border-gray-200 bg-sidebar px-6 py-4">
            <div className="flex items-center space-x-4">
              <p className="text-base leading-6 font-medium">Groups</p>
            </div>

            <PermissionGuard allowedRoles={["admin"]}>
              <Button
                variant="outline"
                leftIcon={<IconPlus size={16} />}
                onClick={createDialogHandler.open}
              >
                Create group
              </Button>
            </PermissionGuard>
          </div>

          <div className="p-6">
            {(organization.isLoading || isLoading) && (
              <div className="flex items-center justify-center">
                <Loader />
              </div>
            )}
            {!organization.isLoading && !isLoading && (
              <>
                {groups?.data && groups.data.length > 0 ? (
                  <div className="space-y-4">
                    {/* Search */}
                    <div className="max-w-md">
                      <Input
                        placeholder="Search groups..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        icon={<IconSearch size={16} />}
                      />
                    </div>

                    {/* Groups List */}
                    <div className="max-h-96 space-y-3 overflow-y-auto">
                      {filteredGroups.length === 0 ? (
                        <div className="py-8 text-center">
                          <p className="text-sm text-muted-foreground">
                            No groups found matching your search.
                          </p>
                        </div>
                      ) : (
                        filteredGroups.map((group) => (
                          <GroupCard
                            key={group.id}
                            group={group}
                            onEdit={() => handleEditGroup(group)}
                            onManageMembers={() => handleManageMembers(group)}
                          />
                        ))
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="py-10">
                    <EmptyState
                      title="No groups yet"
                      subtitle="Create groups to organize your team members and manage access levels."
                      icon={<IconUsers size={48} />}
                      actionButton={
                        <PermissionGuard
                          allowedRoles={["admin"]}
                          fallback={
                            <Button leftIcon={<IconPlus size={16} />} disabled>
                              Create your first group
                            </Button>
                          }
                        >
                          <Button
                            onClick={createDialogHandler.open}
                            leftIcon={<IconPlus size={16} />}
                          >
                            Create your first group
                          </Button>
                        </PermissionGuard>
                      }
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </Card>
      </SettingsSection>

      {/* Dialogs */}
      <CreateGroupDialog
        open={createDialog}
        onClose={createDialogHandler.close}
        organizationId={organization.data?.id ?? ""}
      />

      {selectedGroup && (
        <>
          <EditGroupDialog
            open={editDialog}
            onClose={handleCloseEditDialog}
            group={selectedGroup}
          />

          <ManageGroupMembersDialog
            open={manageMembersDialog}
            onClose={handleCloseMembersDialog}
            group={selectedGroup}
            organizationId={organization.data?.id ?? ""}
          />
        </>
      )}
    </div>
  );
}

interface GroupCardProps {
  group: GroupOutput;
  onEdit: () => void;
  onManageMembers: () => void;
}

function GroupCard({ group, onEdit, onManageMembers }: GroupCardProps) {
  const memberCount = group._count?.groupMembers ?? 0;

  const handleKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      onManageMembers();
    }
  };

  return (
    <div
      className="group flex items-center justify-between rounded-lg border px-4 py-3"
      aria-label={`Manage ${group.name}`}
    >
      <div className="flex min-w-0 items-center gap-3">
        <div className="flex h-8 w-8 items-center justify-center rounded-md bg-muted text-muted-foreground">
          <IconUsers size={16} />
        </div>
        <div className="min-w-0">
          <div className="truncate font-medium">{group.name}</div>
          <div className="mt-0.5 text-xs text-muted-foreground">
            {memberCount} member{memberCount !== 1 ? "s" : ""} • Created{" "}
            {formatDate(group.createdAt)}
          </div>
        </div>
      </div>

      <PermissionGuard allowedRoles={["admin"]}>
        <GroupActionsMenu
          group={group}
          onEdit={onEdit}
          onManageMembers={onManageMembers}
        />
      </PermissionGuard>
    </div>
  );
}
