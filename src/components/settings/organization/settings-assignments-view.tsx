"use client";

import { PermissionGuard } from "@/components/permission-guard";
import { SettingsSection } from "@/components/settings/settings-section";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { EmptyState } from "@/components/ui/empty-state";
import { Input } from "@/components/ui/input";
import { Loader } from "@/components/ui/loader";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetTitle,
} from "@/components/ui/sheet";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useDialog } from "@/hooks/use-dialog";
import { cn } from "@/libs/utils";
import {
  useLocationAssignmentsForMember,
  useMembersWithLocationCount,
  useSetMemberAssignmentsMutation,
} from "@/queries/location-assignment.queries";
import { useLocations } from "@/queries/location.queries";
import { useOrganizationBySlug } from "@/queries/organization.queries";
import { Roles } from "@/types/organization.types";
import { getInitials } from "@/utils/get-initials";
import { getMemberRoleBadgeVariant } from "@/utils/get-member-role-badge-variant";
import { isEmpty } from "@/utils/is-empty";
import { IconMapPin, IconSettings } from "@tabler/icons-react";
import React, { useState } from "react";

export function SettingsAssignmentsView({ slug }: { slug: string }) {
  const organization = useOrganizationBySlug(slug);
  const members = useMembersWithLocationCount(organization.data?.id ?? "");

  const isLoading = members.isLoading || organization.isLoading;

  return (
    <div>
      <SettingsSection>
        <div>
          <h2 className="text-base leading-7 font-semibold">
            Location Assignments
          </h2>
          <p className="mt-1 text-[15px] leading-6 text-gray-500">
            Assign locations to organization members so they only see assigned
            locations.
          </p>
        </div>

        <Card className="md:col-span-2">
          <div className="flex items-center justify-between border-b border-gray-200 bg-sidebar px-6 py-4">
            <p className="text-base leading-6 font-medium">
              Member Assignments
            </p>
          </div>

          <div className="max-h-96 space-y-4 overflow-y-auto">
            {isLoading && (
              <div className="flex items-center justify-center p-6">
                <Loader />
              </div>
            )}

            {!isLoading && !isEmpty(members.data) && (
              <div className="divide-y divide-gray-200">
                {members.data?.map((member) => (
                  <MemberAssignmentCard
                    key={member.id}
                    member={member}
                    orgId={organization.data?.id ?? ""}
                  />
                ))}
              </div>
            )}

            {!isLoading && isEmpty(members.data) && (
              <div className="p-8">
                <EmptyState
                  title="No members found"
                  subtitle="There are no organization members to assign locations to."
                  icon={<IconMapPin size={48} />}
                />
              </div>
            )}
          </div>
        </Card>
      </SettingsSection>
    </div>
  );
}

interface MemberAssignmentCardProps {
  member: {
    id: string;
    name: string;
    email: string;
    role: string;
    image: string | null;
    assignmentCount: number;
  };
  orgId: string;
}

function MemberAssignmentCard({ member, orgId }: MemberAssignmentCardProps) {
  const [manageDialog, manageDialogHandler] = useDialog();

  const isAdmin = member.role === Roles.ADMIN;

  return (
    <div className="flex items-center justify-between px-6 py-4">
      <div className="flex items-center space-x-4">
        <Avatar className="h-10 w-10">
          <AvatarImage src={member?.image ?? ""} />
          <AvatarFallback className="text-white uppercase">
            {getInitials(member.email, 1)}
          </AvatarFallback>
        </Avatar>

        <div>
          <p className="text-sm font-semibold">{member.name}</p>
          <p className="text-sm text-gray-600">{member.email}</p>
        </div>
      </div>

      <div className="flex items-center space-x-3">
        <div className="flex items-center gap-2">
          <Badge
            variant={getMemberRoleBadgeVariant(member.role)}
            className="hidden capitalize md:inline"
          >
            {member.role}
          </Badge>

          {isAdmin ? (
            <Badge variant="gray" className="hidden capitalize md:inline">
              All locations
            </Badge>
          ) : (
            <Badge variant="gray" className="hidden capitalize md:inline">
              {member.assignmentCount}{" "}
              {member.assignmentCount === 1 ? "location" : "locations"}
            </Badge>
          )}
        </div>

        {!isAdmin && (
          <PermissionGuard
            allowedRoles={[Roles.ADMIN]}
            fallback={
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      leftIcon={<IconSettings size={16} />}
                      disabled
                    >
                      Manage
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Only admins can manage location assignments</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            }
          >
            <Button
              variant="outline"
              size="sm"
              leftIcon={<IconSettings size={16} />}
              onClick={manageDialogHandler.open}
            >
              Manage
            </Button>
          </PermissionGuard>
        )}
      </div>

      <ManageAssignmentsSheet
        open={manageDialog}
        onClose={manageDialogHandler.close}
        member={member}
        orgId={orgId}
      />
    </div>
  );
}

interface ManageAssignmentsSheetProps {
  open: boolean;
  onClose: () => void;
  member: {
    id: string;
    name: string;
    email: string;
  };
  orgId: string;
}

function ManageAssignmentsSheet({
  open,
  onClose,
  member,
  orgId,
}: ManageAssignmentsSheetProps) {
  const [selectedLocationIds, setSelectedLocationIds] = useState<string[]>([]);
  const [initialLocationIds, setInitialLocationIds] = useState<string[]>([]);
  const [query, setQuery] = useState("");
  const [internalOpen, setInternalOpen] = useState(open);
  const [activeTab, setActiveTab] = useState("available");

  const locations = useLocations({
    organizationId: orgId,
    searchString: query,
    take: 500,
  });
  const assignments = useLocationAssignmentsForMember(orgId, member.id);
  const setAssignmentsMutation = useSetMemberAssignmentsMutation();

  // Initialize selected locations when assignments load
  React.useEffect(() => {
    if (assignments.data) {
      const assignedIds = assignments.data.map((a) => a.locationId);
      setSelectedLocationIds(assignedIds);
      setInitialLocationIds(assignedIds);
    }
  }, [assignments.data]);

  // Sync internal open with external
  React.useEffect(() => {
    setInternalOpen(open);
  }, [open]);

  const handleClose = () => {
    // Reset to initial values when closing
    setSelectedLocationIds(initialLocationIds);
    setQuery("");
    setActiveTab("available");
    onClose();
  };

  const handleLocationToggle = (locationId: string) => {
    setSelectedLocationIds((prev) =>
      prev.includes(locationId)
        ? prev.filter((id) => id !== locationId)
        : [...prev, locationId],
    );
  };

  const handleSelectAll = () => {
    if (locations.data?.data) {
      setSelectedLocationIds(locations.data.data.map((l) => l.id));
    }
  };

  const handleClearAll = () => {
    if (activeTab === "assigned") {
      // Remove all assigned locations from selected
      const assignedIds = assignedLocations.map((l) => l.id);
      setSelectedLocationIds(
        selectedLocationIds.filter((id) => !assignedIds.includes(id)),
      );
    } else {
      // Clear all selections
      setSelectedLocationIds([]);
    }
  };

  const handleSave = async () => {
    try {
      await setAssignmentsMutation.mutateAsync({
        orgId,
        memberId: member.id,
        locationIds: selectedLocationIds,
      });
      // Update initial values to current values after successful save
      setInitialLocationIds(selectedLocationIds);
      onClose();
    } catch (error) {
      // Error handling is in the mutation
      console.error("Error setting member assignments:", error);
    }
  };

  // Filter locations based on query and tab. Use memo to avoid re-filtering
  const { availableLocations, assignedLocations } = React.useMemo(() => {
    if (!locations.data?.data)
      return { availableLocations: [], assignedLocations: [] };

    const q = query.trim().toLowerCase();
    const allLocations = locations.data.data;
    const filteredLocations = q
      ? allLocations.filter((l) => l.name.toLowerCase().includes(q))
      : allLocations;

    // Available tab shows ALL locations (with their assignment status)
    const available = filteredLocations;

    // Assigned tab shows ONLY assigned locations
    const assigned = filteredLocations.filter((l) =>
      selectedLocationIds.includes(l.id),
    );

    return { availableLocations: available, assignedLocations: assigned };
  }, [locations.data?.data, query, selectedLocationIds]);

  const isFetching = locations.isLoading || assignments.isLoading;

  return (
    <Sheet
      open={internalOpen}
      onOpenChange={(val) => {
        if (!val) handleClose();
        setInternalOpen(val);
      }}
    >
      <SheetContent side="right" className="flex h-full w-[500px] flex-col p-0">
        {/* Header */}
        <div className="border-b px-6 py-5">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <SheetTitle className="text-lg font-semibold">
                Location Assignments
              </SheetTitle>
              <SheetDescription className="text-sm text-muted-foreground">
                Manage location access for {member.name}
              </SheetDescription>
            </div>
          </div>
        </div>

        {/* Search and Actions */}
        <div className="border-b px-6 pt-2 pb-6">
          <div className="space-y-3">
            <Input
              placeholder="Search locations..."
              value={query}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setQuery(e.target.value)
              }
              className="h-10 bg-background"
            />
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectAll}
                disabled={isFetching || isEmpty(locations.data?.data)}
                className="flex-1"
              >
                Select All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearAll}
                disabled={
                  activeTab === "assigned"
                    ? isEmpty(assignedLocations)
                    : selectedLocationIds.length === 0
                }
                className="flex-1"
              >
                {activeTab === "assigned" ? "Unassign All" : "Clear All"}
              </Button>
            </div>
          </div>
        </div>

        {/* Location Tabs */}
        <div className="flex-1 overflow-hidden">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="flex h-full flex-col"
          >
            <div className="px-6 pb-4">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="available">All Locations</TabsTrigger>
                <TabsTrigger value="assigned">Assigned Only</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent
              value="available"
              className="mt-0 flex-1 overflow-hidden"
            >
              <div className="flex items-center justify-between px-6 pb-4">
                <div className="space-y-1">
                  <h3 className="text-base font-medium text-foreground">
                    All Locations
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    View and manage all location assignments
                  </p>
                </div>

                <div className="rounded-lg bg-primary/5 px-3 py-2">
                  <div className="text-xs font-medium text-primary">
                    {selectedLocationIds.length} of {availableLocations.length}
                  </div>
                  <div className="text-xs text-muted-foreground">assigned</div>
                </div>
              </div>

              <ScrollArea className="h-full px-6 pb-24">
                {isFetching && (
                  <div className="flex items-center justify-center py-12">
                    <Loader />
                  </div>
                )}

                {!isFetching && isEmpty(availableLocations) && (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <IconMapPin className="h-8 w-8 text-muted-foreground/50" />
                    <p className="mt-2 text-sm text-muted-foreground">
                      {query
                        ? "No locations match your search"
                        : "No locations available"}
                    </p>
                  </div>
                )}

                <div className="space-y-2">
                  {availableLocations.map((location) => {
                    const checked = selectedLocationIds.includes(location.id);
                    return (
                      <div
                        key={location.id}
                        className={cn(
                          "group relative flex cursor-pointer items-center space-x-3 rounded-lg border border-border p-4 transition-all hover:bg-muted/50",
                          checked && "bg-primary/5 hover:bg-primary/5",
                        )}
                        onClick={() => handleLocationToggle(location.id)}
                        role="button"
                      >
                        <Checkbox checked={checked} className="h-5 w-5" />
                        <div className="flex min-w-0 flex-1 cursor-pointer items-center justify-between text-left">
                          <div className="min-w-0 flex-1">
                            <p className="text-sm font-medium text-foreground">
                              {location.name}
                            </p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent
              value="assigned"
              className="mt-0 flex-1 overflow-hidden"
            >
              <div className="flex items-center justify-between px-6 pb-4">
                <div className="space-y-1">
                  <h3 className="text-base font-medium text-foreground">
                    Assigned Locations
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Locations currently assigned to this member
                  </p>
                </div>

                <div className="rounded-lg bg-primary/5 px-3 py-2">
                  <div className="text-xs font-medium text-primary">
                    {assignedLocations.length}
                  </div>
                  <div className="text-xs text-muted-foreground">assigned</div>
                </div>
              </div>

              <ScrollArea className="h-full px-6 pb-24">
                {isFetching && (
                  <div className="flex items-center justify-center py-12">
                    <Loader />
                  </div>
                )}

                {!isFetching && isEmpty(assignedLocations) && (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <IconMapPin className="h-8 w-8 text-muted-foreground/50" />
                    <p className="mt-2 text-sm text-muted-foreground">
                      {query
                        ? "No assigned locations match your search"
                        : "No locations assigned yet"}
                    </p>
                  </div>
                )}

                <div className="space-y-2">
                  {assignedLocations.map((location) => (
                    <div
                      key={location.id}
                      className="group relative flex cursor-pointer items-center space-x-3 rounded-lg border border-border bg-primary/5 p-4 transition-all hover:bg-muted/50"
                      onClick={() => handleLocationToggle(location.id)}
                      role="button"
                    >
                      <Checkbox checked={true} className="h-5 w-5" />
                      <div className="flex min-w-0 flex-1 cursor-pointer items-center justify-between text-left">
                        <div className="min-w-0 flex-1">
                          <p className="text-sm font-medium text-foreground">
                            {location.name}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>

        {/* Footer Actions */}
        <div className="border-t px-6 py-4">
          <div className="flex gap-3">
            <Button variant="outline" onClick={handleClose} className="flex-1">
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              loading={setAssignmentsMutation.isPending}
              disabled={setAssignmentsMutation.isPending}
              className="flex-1"
            >
              Save changes
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
