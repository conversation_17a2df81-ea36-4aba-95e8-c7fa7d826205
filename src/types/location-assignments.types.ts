import type { RouterInputs, RouterOutputs } from "@/trpc/react";

export type LocationAssignmentGetForMemberInput =
  RouterInputs["locationAssignments"]["getForMember"];
export type LocationAssignmentGetMyAssignmentsInput =
  RouterInputs["locationAssignments"]["getMyAssignments"];
export type LocationAssignmentSetForMemberInput =
  RouterInputs["locationAssignments"]["setForMember"];
export type LocationAssignmentAssignManyInput =
  RouterInputs["locationAssignments"]["assignMany"];
export type LocationAssignmentRevokeInput =
  RouterInputs["locationAssignments"]["revoke"];
export type LocationAssignmentGetAssignedMembersInput =
  RouterInputs["locationAssignments"]["getAssignedMembersForLocation"];

export type LocationAssignmentGetForMemberOutput =
  RouterOutputs["locationAssignments"]["getForMember"];
export type LocationAssignmentGetMyAssignmentsOutput =
  RouterOutputs["locationAssignments"]["getMyAssignments"];
export type LocationAssignmentSetForMemberOutput =
  RouterOutputs["locationAssignments"]["setForMember"];
export type LocationAssignmentAssignManyOutput =
  RouterOutputs["locationAssignments"]["assignMany"];
export type LocationAssignmentRevokeOutput =
  RouterOutputs["locationAssignments"]["revoke"];
export type LocationAssignmentGetAssignedMembersOutput =
  RouterOutputs["locationAssignments"]["getAssignedMembersForLocation"];
