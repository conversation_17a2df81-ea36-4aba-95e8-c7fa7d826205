import type { RouterInputs, RouterOutputs } from "@/trpc/react";
import type { InfiniteData } from "@tanstack/react-query";

export type ApprovalStatus = "pending" | "approved" | "rejected";

export type NotificationType = "approval_requested" | "approved" | "rejected";

export interface ApprovalFilter {
  status?: ApprovalStatus | "all";
  projectId?: string;
  cursor?: string;
  take?: number;
}

// Router Input Types
export type ApprovalSubmitInput =
  RouterInputs["approvals"]["submitForApproval"];

export type ApprovalReviewInput = RouterInputs["approvals"]["reviewApproval"];

export type ApprovalsGetAllInput = RouterInputs["approvals"]["getAll"];

export type ApprovalGetByIdInput = RouterInputs["approvals"]["getById"];

export type PendingReviewsInput =
  RouterInputs["approvals"]["getPendingReviews"];

// Router Output Types
export type ApprovalSubmitOutput =
  RouterOutputs["approvals"]["submitForApproval"];

export type ApprovalReviewOutput = RouterOutputs["approvals"]["reviewApproval"];

export type ApprovalsGetAllOutput = RouterOutputs["approvals"]["getAll"];

export type ApprovalGetByIdOutput = RouterOutputs["approvals"]["getById"];

export type ApprovalReview = RouterOutputs["approvals"]["getPendingReviews"][0];

export type PendingReviewsOutput =
  RouterOutputs["approvals"]["getPendingReviews"];

// Infinite Query Types
export type InfiniteApprovalsData = InfiniteData<ApprovalsGetAllOutput>;

// Individual approval items from the output arrays
export type ApprovalItem = ApprovalsGetAllOutput["data"][0];

export type PendingReviewItem = PendingReviewsOutput[0];

// New types for rejected projects
export type RejectedProjectsInput =
  RouterInputs["approvals"]["getRejectedProjects"];

export type RejectedProjectsOutput =
  RouterOutputs["approvals"]["getRejectedProjects"];

export type RejectedProject = RejectedProjectsOutput[0];

export type RejectionHistoryInput =
  RouterInputs["approvals"]["getRejectionHistory"];

export type RejectionHistoryOutput =
  RouterOutputs["approvals"]["getRejectionHistory"];

export type RejectionHistoryItem = RejectionHistoryOutput[0];
