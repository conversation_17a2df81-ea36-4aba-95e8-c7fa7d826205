import type { APPROVAL_STATUS } from "@/libs/constants";
import type { RouterInputs, RouterOutputs } from "@/trpc/react";
import type { InfiniteData } from "@tanstack/react-query";

export type ProjectCreateInput = RouterInputs["projects"]["create"];

export type ProjectUpdateInput = RouterInputs["projects"]["updateById"];

export type ProjectsFindInput = RouterInputs["projects"]["getAll"];

export type Project = RouterOutputs["projects"]["getById"];

export type ProjectsOutput = RouterOutputs["projects"]["getAll"];

export type InfiniteProjectsData = InfiniteData<ProjectsOutput>;

export type ProjectStatus = ProjectCreateInput["status"];

export type ProjectApprovalStatus =
  (typeof APPROVAL_STATUS)[keyof typeof APPROVAL_STATUS];

export type ProjectMetrics = RouterOutputs["projects"]["getMetrics"];

export type ProjectActivityStats =
  RouterOutputs["projects"]["getActivityStats"];

export type ActiveProject =
  RouterOutputs["projects"]["getAllActive"]["data"][0];

export type ProjectActivityTimeline =
  RouterOutputs["projects"]["getProjectActivityTimeline"];

export type ProjectResubmissionInput =
  RouterInputs["projects"]["resubmitForApproval"];
