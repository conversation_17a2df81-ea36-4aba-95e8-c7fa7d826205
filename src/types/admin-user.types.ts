// Types for Better Auth admin user management

export interface ListUsersParams {
  searchValue?: string;
  searchField?: "email" | "name";
  searchOperator?: "contains" | "starts_with" | "ends_with";
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDirection?: "asc" | "desc";
  filterField?: string;
  filterValue?: string | number | boolean;
  filterOperator?: "eq" | "ne" | "lt" | "lte" | "gt" | "gte";
}

export interface User {
  id: string;
  email: string;
  name: string;
  image?: string;
  role?: "admin" | "user" | ["admin"] | ["user"] | ["admin", "user"];
  banned?: boolean;
  banReason?: string;
  banExpires?: string;
  // Add other fields as needed
}

export interface ListUsersResponse {
  users: User[];
  total: number;
  limit?: number;
  offset?: number;
}

export interface CreateUserParams {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: "admin" | "user" | ["admin"] | ["user"] | ["admin", "user"];
  data?: Record<string, unknown>;
}

export interface SetRoleParams {
  userId: string;
  role: "admin" | "user" | ["admin"] | ["user"] | ["admin", "user"];
}

export interface SetUserPasswordParams {
  userId: string;
  newPassword: string;
}

export interface BanUserParams {
  userId: string;
  banReason?: string;
  banExpiresIn?: number;
}

export interface UnbanUserParams {
  userId: string;
}

export interface RemoveUserParams {
  userId: string;
}

export interface ListUserSessionsParams {
  userId: string;
}

export interface RevokeUserSessionParams {
  sessionToken: string;
}

export interface RevokeUserSessionsParams {
  userId: string;
}

export interface ImpersonateUserParams {
  userId: string;
}

export type StopImpersonatingParams = object;

export interface UserHasPermissionParams {
  userId?: string;
  role?: "admin" | "user";
  permission?: Record<string, string[]>;
  permissions?: Record<string, string[]>;
}
