import type { RouterInputs, RouterOutputs } from "@/trpc/react";
import type { InfiniteData } from "@tanstack/react-query";

export type GroupCreateInput = RouterInputs["groups"]["create"];
export type GroupUpdateInput = RouterInputs["groups"]["updateById"];
export type GroupsFindInput = RouterInputs["groups"]["getAll"];

export type GroupsOutput = RouterOutputs["groups"]["getAll"];
export type GroupOutput = RouterOutputs["groups"]["getById"];

export type GroupMembersOutput = RouterOutputs["groups"]["getMembers"];
export type GroupMemberOutput = GroupMembersOutput["data"][0];

export type GroupsForMemberOutput = RouterOutputs["groups"]["getForMember"];

export type InfiniteGroupsData = InfiniteData<GroupsOutput> | undefined;
export type InfiniteGroupMembersData =
  | InfiniteData<GroupMembersOutput>
  | undefined;
