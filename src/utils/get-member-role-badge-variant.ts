import { Roles, type Role } from "@/types/organization.types";

/**
 * Badge variants used to visually represent organization roles in the UI.
 *
 * These correspond to the `variant` values supported by the local Badge component
 * at `@/components/ui/badge`.
 */
export type MemberRoleBadgeVariant = "blue" | "yellow" | "green" | "gray";

/**
 * Returns the Badge variant to use for a given organization member role.
 *
 * Mapping:
 * - admin   -> "blue"
 * - manager -> "yellow"
 * - member  -> "green"
 * - other/unknown/empty -> "gray" (fallback)
 *
 * Behavior:
 * - Accepts `Role` (from `@/types/organization.types`) or string values.
 * - Case-insensitive; `"Admin"`, `"ADMIN"`, and `Roles.ADMIN` are all handled.
 * - Safely handles `null` and `undefined` by returning the fallback.
 *
 * @param role - The organization role to map to a Badge variant.
 * @returns The corresponding Badge variant string to pass to the Badge component.
 *
 * @example
 *   import { Badge } from "@/components/ui/badge";
 *   import { Roles } from "@/types/organization.types";
 *   import { getMemberRoleBadgeVariant } from "@/utils/get-member-role-badge-variant";
 *
 *   <Badge variant={getMemberRoleBadgeVariant(Roles.ADMIN)} className="capitalize">
 *     admin
 *   </Badge>
 *
 *   <Badge variant={getMemberRoleBadgeVariant("manager")}>manager</Badge>
 */
export function getMemberRoleBadgeVariant(
  role?: Role | string | null,
): MemberRoleBadgeVariant {
  const r = String(role ?? "").toLowerCase();
  switch (r) {
    case Roles.ADMIN:
    case "admin":
      return "blue";
    case Roles.MANAGER:
    case "manager":
      return "yellow";
    case Roles.MEMBER:
    case "member":
      return "green";
    default:
      return "gray";
  }
}
