import { authClient } from "@/libs/auth-client";
import type {
  BanUserParams,
  CreateUserParams,
  ImpersonateUserParams,
  ListUserSessionsParams,
  ListUsersParams,
  ListUsersResponse,
  RemoveUserParams,
  RevokeUserSessionParams,
  RevokeUserSessionsParams,
  SetRoleParams,
  SetUserPasswordParams,
  StopImpersonatingParams,
  UnbanUserParams,
  UserHasPermissionParams,
} from "@/types/admin-user.types";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// List users
export function useListUsers(params: ListUsersParams) {
  return useQuery<ListUsersResponse, Error>({
    queryKey: ["admin", "list-users", params],
    queryFn: async () => {
      const response = await authClient.admin.listUsers({
        query: {
          ...params,
          sortBy: "createdAt",
          sortDirection: "desc",
        },
      });
      if (response?.data && Array.isArray(response.data.users)) {
        const mappedUsers = response.data.users.map((user: any) => ({
          ...user,
          role:
            user.role === "admin" || user.role === "user"
              ? user.role
              : Array.isArray(user.role)
                ? user.role.filter((r: string) => r === "admin" || r === "user")
                : undefined,
        }));
        return {
          ...response.data,
          users: mappedUsers,
        };
      }
      return {
        users: [],
        total: 0,
        limit: params.limit,
        offset: params.offset,
      };
    },
    staleTime: 30_000,
  });
}

// Create user
export function useCreateUser() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (params: CreateUserParams) => {
      return await authClient.admin.createUser({
        ...params,
        name: `${params.firstName} ${params.lastName}`,
        data: {
          firstName: params.firstName,
          lastName: params.lastName,
        },
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin", "list-users"] });
    },
  });
}

// Set user role
export function useSetUserRole() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (params: SetRoleParams) => {
      return await authClient.admin.setRole(params);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin", "list-users"] });
    },
  });
}

// Set user password
export function useSetUserPassword() {
  return useMutation({
    mutationFn: async (params: SetUserPasswordParams) => {
      return await authClient.admin.setUserPassword(params);
    },
  });
}

// Ban user
export function useBanUser() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (params: BanUserParams) => {
      return await authClient.admin.banUser(params);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin", "list-users"] });
    },
  });
}

// Unban user
export function useUnbanUser() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (params: UnbanUserParams) => {
      return await authClient.admin.unbanUser(params);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin", "list-users"] });
    },
  });
}

// Remove user
export function useRemoveUser() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (params: RemoveUserParams) => {
      return await authClient.admin.removeUser(params);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin", "list-users"] });
    },
  });
}

// List user sessions
export function useListUserSessions(params: ListUserSessionsParams) {
  return useQuery({
    queryKey: ["admin", "list-user-sessions", params],
    queryFn: async () => {
      return await authClient.admin.listUserSessions(params);
    },
    staleTime: 30_000,
  });
}

// Revoke user session
export function useRevokeUserSession() {
  return useMutation({
    mutationFn: async (params: RevokeUserSessionParams) => {
      return await authClient.admin.revokeUserSession(params);
    },
  });
}

// Revoke all user sessions
export function useRevokeUserSessions() {
  return useMutation({
    mutationFn: async (params: RevokeUserSessionsParams) => {
      return await authClient.admin.revokeUserSessions(params);
    },
  });
}

// Impersonate user
export function useImpersonateUser() {
  return useMutation({
    mutationFn: async (params: ImpersonateUserParams) => {
      return await authClient.admin.impersonateUser(params);
    },
  });
}

// Stop impersonating
export function useStopImpersonating() {
  return useMutation({
    mutationFn: async (params: StopImpersonatingParams) => {
      return await authClient.admin.stopImpersonating(params);
    },
  });
}

// Check user permissions
export function useUserHasPermission() {
  return useMutation({
    mutationFn: async (params: UserHasPermissionParams) => {
      return await authClient.admin.hasPermission(params);
    },
  });
}
