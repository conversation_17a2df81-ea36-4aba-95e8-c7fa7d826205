import { api } from "@/trpc/react";
import type {
  GroupMembersOutput,
  GroupsFindInput,
  GroupsOutput,
} from "@/types/group.types";
import { isEmpty } from "@/utils/is-empty";
import { toast } from "sonner";

export const useGroupById = (id: string) => {
  return api.groups.getById.useQuery({ id }, { enabled: !isEmpty(id) });
};

export function useGroups(input: GroupsFindInput) {
  return api.groups.getAll.useQuery(
    { ...input },
    { enabled: !isEmpty(input?.organizationId) },
  );
}

export function useInfiniteGroups(
  input?: GroupsFindInput,
  initialData?: GroupsOutput,
) {
  return api.groups.getAll.useInfiniteQuery(
    { ...input, organizationId: input?.organizationId ?? "" },
    {
      enabled: !isEmpty(input?.organizationId),
      initialData: () => {
        if (initialData) {
          return { pageParams: [undefined], pages: [initialData] };
        }
      },
      getNextPageParam: (lastPage) => lastPage?.cursor || undefined,
    },
  );
}

export const useGroupCreateMutation = () => {
  const utils = api.useUtils();

  return api.groups.create.useMutation({
    onError: (error) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await utils.groups.getAll.invalidate();
      await utils.groups.getForMember.invalidate();
    },
  });
};

export const useGroupUpdateMutation = () => {
  const utils = api.useUtils();

  return api.groups.updateById.useMutation({
    onError: (error) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, input) => {
      await utils.groups.getById.invalidate({ id: input.id });
      await utils.groups.getAll.invalidate();
      await utils.groups.getForMember.invalidate();
    },
  });
};

export const useGroupDeleteMutation = () => {
  const utils = api.useUtils();

  return api.groups.deleteById.useMutation({
    onError: (error) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, input) => {
      await utils.groups.getById.invalidate({ id: input.id });
      await utils.groups.getAll.invalidate();
      await utils.groups.getForMember.invalidate();
    },
  });
};

export const useGroupAddMemberMutation = () => {
  const utils = api.useUtils();

  return api.groups.addMember.useMutation({
    onError: (error) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, input) => {
      // input contains groupId and memberId
      await utils.groups.getMembers.invalidate({
        groupId: input.groupId,
      });
      await utils.groups.getAll.invalidate();
      await utils.groups.getForMember.invalidate();
    },
  });
};

export const useGroupAddMembersMutation = () => {
  const utils = api.useUtils();

  return api.groups.addMembers.useMutation({
    onError: (error) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, input) => {
      await utils.groups.getMembers.invalidate({
        groupId: input.groupId,
      });
      await utils.groups.getAll.invalidate();
      await utils.groups.getForMember.invalidate();
    },
  });
};

export const useGroupRemoveMemberMutation = () => {
  const utils = api.useUtils();

  return api.groups.removeMember.useMutation({
    onError: (error) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, input) => {
      await utils.groups.getMembers.invalidate({
        groupId: input.groupId,
      });
      await utils.groups.getAll.invalidate();
      await utils.groups.getForMember.invalidate();
    },
  });
};

export function useGroupMembers(input: { groupId?: string; take?: number }) {
  return api.groups.getMembers.useQuery(
    { groupId: input.groupId ?? "", take: input.take },
    { enabled: !isEmpty(input.groupId) },
  );
}

export function useInfiniteGroupMembers(
  input?: {
    groupId?: string;
    cursor?: string;
    take?: number;
    sort?: "asc" | "desc";
  },
  initialData?: GroupMembersOutput,
) {
  return api.groups.getMembers.useInfiniteQuery(
    { ...(input ?? {}), groupId: input?.groupId ?? "" },
    {
      enabled: !isEmpty(input?.groupId),
      initialData: () => {
        if (initialData) {
          return { pageParams: [undefined], pages: [initialData] };
        }
      },
      getNextPageParam: (lastPage) => lastPage?.cursor || undefined,
    },
  );
}

export function useGroupsForMember(organizationId?: string, memberId?: string) {
  return api.groups.getForMember.useQuery(
    { organizationId: organizationId ?? "", memberId: memberId ?? "" },
    { enabled: !isEmpty(organizationId) && !isEmpty(memberId) },
  );
}
