import { api } from "@/trpc/react";
import { toast } from "sonner";

// Queries
export const useLocationAssignmentsForMember = (
  orgId: string,
  memberId: string,
) => {
  return api.locationAssignments.getForMember.useQuery(
    { orgId, memberId },
    { enabled: !!orgId && !!memberId },
  );
};

export const useMyLocationAssignments = (orgId: string) => {
  return api.locationAssignments.getMyAssignments.useQuery(
    { orgId },
    { enabled: !!orgId },
  );
};

export const useAssignedMembersForLocation = (
  orgId: string,
  locationId: string,
) => {
  return api.locationAssignments.getAssignedMembersForLocation.useQuery(
    { orgId, locationId },
    { enabled: !!orgId && !!locationId },
  );
};

export const useMembersWithLocationCount = (orgId: string) => {
  return api.locationAssignments.getMembersWithLocationCount.useQuery(
    { orgId },
    { enabled: !!orgId },
  );
};

// Mutations
export const useSetMemberAssignmentsMutation = () => {
  const apiUtils = api.useUtils();

  return api.locationAssignments.setForMember.useMutation({
    onMutate: async (input) => {
      await apiUtils.locationAssignments.getForMember.cancel({
        orgId: input.orgId,
        memberId: input.memberId,
      });
      const previous = apiUtils.locationAssignments.getForMember.getData({
        orgId: input.orgId,
        memberId: input.memberId,
      });
      return { previous };
    },
    onError: (err, input, ctx) => {
      apiUtils.locationAssignments.getForMember.setData(
        { orgId: input.orgId, memberId: input.memberId },
        ctx?.previous,
      );
      toast.error("Error updating assignments", { description: err.message });
    },
    onSuccess: () => {
      toast.success("Assignments updated");
    },
    onSettled: async (data, error, input) => {
      await apiUtils.locationAssignments.getForMember.invalidate({
        orgId: input.orgId,
        memberId: input.memberId,
      });
      await apiUtils.locationAssignments.getMembersWithLocationCount.invalidate(
        {
          orgId: input.orgId,
        },
      );
      await apiUtils.organizations.getMembers.invalidate({ id: input.orgId });
    },
  });
};

export const useAssignManyMutation = () => {
  const apiUtils = api.useUtils();

  return api.locationAssignments.assignMany.useMutation({
    onMutate: async (input) => {
      await apiUtils.locationAssignments.getForMember.cancel({
        orgId: input.orgId,
        memberId: input.memberId,
      });
      const previous = apiUtils.locationAssignments.getForMember.getData({
        orgId: input.orgId,
        memberId: input.memberId,
      });
      return { previous };
    },
    onError: (err, input, ctx) => {
      apiUtils.locationAssignments.getForMember.setData(
        { orgId: input.orgId, memberId: input.memberId },
        ctx?.previous,
      );
      toast.error("Error assigning locations", { description: err.message });
    },
    onSuccess: () => {
      toast.success("Locations assigned");
    },
    onSettled: async (data, error, input) => {
      await apiUtils.locationAssignments.getForMember.invalidate({
        orgId: input.orgId,
        memberId: input.memberId,
      });
      await apiUtils.locationAssignments.getMembersWithLocationCount.invalidate(
        {
          orgId: input.orgId,
        },
      );
      await apiUtils.organizations.getMembers.invalidate({ id: input.orgId });
    },
  });
};

export const useRevokeAssignmentMutation = () => {
  const apiUtils = api.useUtils();

  return api.locationAssignments.revoke.useMutation({
    onMutate: async (input) => {
      await apiUtils.locationAssignments.getForMember.cancel({
        orgId: input.orgId,
        memberId: input.memberId,
      });
      const previous = apiUtils.locationAssignments.getForMember.getData({
        orgId: input.orgId,
        memberId: input.memberId,
      });
      return { previous };
    },
    onError: (err, input, ctx) => {
      apiUtils.locationAssignments.getForMember.setData(
        { orgId: input.orgId, memberId: input.memberId },
        ctx?.previous,
      );
      toast.error("Error revoking assignment", { description: err.message });
    },
    onSuccess: () => {
      toast.success("Assignment revoked");
    },
    onSettled: async (data, error, input) => {
      await apiUtils.locationAssignments.getForMember.invalidate({
        orgId: input.orgId,
        memberId: input.memberId,
      });
      await apiUtils.locationAssignments.getMembersWithLocationCount.invalidate(
        {
          orgId: input.orgId,
        },
      );
      await apiUtils.organizations.getMembers.invalidate({ id: input.orgId });
    },
  });
};
