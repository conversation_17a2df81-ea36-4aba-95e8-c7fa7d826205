import { z } from "@/libs/zod";

export const ProjectApprovalRequestSchema = z.object({
  projectId: z.string().min(1, { message: "Project ID is required" }),
  comments: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  locations: z.array(z.string()).optional(),
  sublocations: z.array(z.string()).optional(),
});

export const ProjectApprovalReviewSchema = z.object({
  approvalId: z.string().min(1, { message: "Approval ID is required" }),
  status: z.enum(["approved", "rejected"]),
  reviewComments: z.string().optional(),
});

export const ApprovalFilterSchema = z.object({
  status: z.enum(["pending", "approved", "rejected", "all"]).optional(),
  projectId: z.string().optional(),
});

export const ProjectResubmissionSchema = z.object({
  projectId: z.string().min(1, { message: "Project ID is required" }),
  comments: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  locations: z.array(z.string()).optional(),
  sublocations: z.array(z.string()).optional(),
  name: z.string().min(1, { message: "Project name is required" }).optional(),
});
