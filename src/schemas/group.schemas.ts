import { z } from "@/libs/zod";
import { filterSchema } from "./api.schemas";

export const GroupCreateSchema = z.object({
  name: z.string().min(1, "Group name is required"),
  organizationId: z.string(),
});

export const GroupUpdateSchema = GroupCreateSchema.partial().extend({
  id: z.string(),
});

export const GroupFindSchema = z.object({
  organizationId: z.string(),
  ...filterSchema,
});

export const GroupGetByIdSchema = z.object({ id: z.string() });

export const GroupDeleteSchema = z.object({ id: z.string() });

export const GroupAddMemberSchema = z.object({
  organizationId: z.string(),
  groupId: z.string(),
  memberId: z.string(),
});

export const GroupAddMembersSchema = z.object({
  organizationId: z.string(),
  groupId: z.string(),
  memberIds: z.array(z.string()).min(1),
});

export const GroupRemoveMemberSchema = z.object({
  organizationId: z.string(),
  groupId: z.string(),
  memberId: z.string(),
});

export const GroupGetMembersSchema = z.object({
  groupId: z.string(),
  ...filterSchema,
});

export const GroupGetForMemberSchema = z.object({
  organizationId: z.string(),
  memberId: z.string(),
  ...filterSchema,
});
