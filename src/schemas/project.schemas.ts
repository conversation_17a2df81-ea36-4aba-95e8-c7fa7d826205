import { z } from "@/libs/zod";

export const ProjectCreateSchema = z.object({
  name: z.string().min(1, { message: "Project name is required" }),
  status: z
    .enum([
      "editing",
      "approved",
      "active",
      "pending_approval",
      "completed",
      "rejected",
    ])
    .optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
});

export const ProjectUpdateSchema = ProjectCreateSchema.partial();
