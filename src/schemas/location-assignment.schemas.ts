import { z } from "zod";

export const LocationAssignmentGetForMemberSchema = z.object({
  orgId: z.string(),
  memberId: z.string(),
});

export const LocationAssignmentGetMyAssignmentsSchema = z.object({
  orgId: z.string(),
});

export const LocationAssignmentSetForMemberSchema = z.object({
  orgId: z.string(),
  memberId: z.string(),
  locationIds: z.array(z.string()),
});

export const LocationAssignmentAssignManySchema = z.object({
  orgId: z.string(),
  memberId: z.string(),
  locationIds: z.array(z.string()),
});

export const LocationAssignmentRevokeSchema = z.object({
  orgId: z.string(),
  memberId: z.string(),
  locationId: z.string(),
});

export const LocationAssignmentGetAssignedMembersSchema = z.object({
  orgId: z.string(),
  locationId: z.string(),
});
