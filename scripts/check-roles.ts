#!/usr/bin/env bun

/**
 * Simple script to check all users and their organization roles
 * 
 * Usage:
 * bun run scripts/check-roles.ts
 */

import { db } from "../src/server/db";

async function main() {
  try {
    console.log("🔍 Checking all users and their organization roles...\n");

    // Get all users with their organization memberships
    const users = await db.user.findMany({
      where: {
        deletedAt: null,
      },
      select: {
        id: true,
        name: true,
        email: true,
        members: {
          where: { deletedAt: null },
          select: {
            id: true,
            role: true,
            createdAt: true,
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        },
      },
    });

    if (users.length === 0) {
      console.log("❌ No users found");
      return;
    }

    console.log(`📋 Found ${users.length} users:\n`);

    for (const user of users) {
      console.log(`👤 ${user.name} (${user.email})`);
      console.log(`   User ID: ${user.id}`);
      
      if (user.members.length === 0) {
        console.log("   ❌ Not a member of any organizations");
      } else {
        console.log(`   📋 Member of ${user.members.length} organization(s):`);
        
        for (const member of user.members) {
          const org = member.organization;
          console.log(`      🏢 ${org.name} (${org.slug})`);
          console.log(`         Role: ${member.role}`);
          console.log(`         Org ID: ${org.id}`);
          console.log(`         Member ID: ${member.id}`);
          
          if (member.role === 'admin') {
            console.log(`         ✅ ADMIN ACCESS`);
          } else {
            console.log(`         ⚠️  Limited access (${member.role})`);
          }
        }
      }
      console.log("");
    }

    // Also check Better Auth dynamic roles
    console.log("🔧 Checking Better Auth dynamic roles...\n");
    
    const betterAuthRoles = await db.organizationRole.findMany({
      select: {
        id: true,
        role: true,
        permission: true,
        createdAt: true,
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    if (betterAuthRoles.length === 0) {
      console.log("❌ No Better Auth dynamic roles found");
    } else {
      console.log(`📝 Found ${betterAuthRoles.length} Better Auth dynamic role(s):`);
      
      for (const role of betterAuthRoles) {
        console.log(`   🎭 Role: ${role.role}`);
        console.log(`      Organization: ${role.organization.name} (${role.organization.slug})`);
        console.log(`      Created: ${role.createdAt.toISOString()}`);
        console.log(`      Permissions: ${JSON.stringify(role.permission, null, 6)}`);
        console.log("");
      }
    }

    // Provide troubleshooting suggestions
    console.log("🔧 Troubleshooting suggestions:");
    console.log("");
    
    const adminUsers = users.filter(u => u.members.some(m => m.role === 'admin'));
    const nonAdminUsers = users.filter(u => u.members.length > 0 && !u.members.some(m => m.role === 'admin'));
    
    if (adminUsers.length === 0) {
      console.log("❌ No admin users found!");
      console.log("   To fix: Update a user's role to 'admin' in the database");
      if (users.length > 0 && users[0].members.length > 0) {
        const firstMember = users[0].members[0];
        console.log(`   Example SQL: UPDATE members SET role = 'admin' WHERE id = '${firstMember.id}';`);
      }
    } else {
      console.log(`✅ Found ${adminUsers.length} admin user(s)`);
    }
    
    if (nonAdminUsers.length > 0) {
      console.log(`⚠️  Found ${nonAdminUsers.length} non-admin user(s) who might need admin access`);
    }
    
    console.log("");
    console.log("📋 Next steps if you're still getting permission errors:");
    console.log("1. Verify your user has 'admin' role in the organization");
    console.log("2. Check that Better Auth admin role includes required permissions");
    console.log("3. Ensure dynamicAccessControl is properly configured");
    console.log("4. Check browser network tab for the exact API call failing");

  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await db.$disconnect();
  }
}

main().catch(console.error);
