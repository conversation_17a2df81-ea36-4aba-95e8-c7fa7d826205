import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

function printUsage() {
  console.log(
    "Usage: bun run scripts/add-user-to-organization.ts <organizationIdOrSlug> <role> <email1> [email2 ...]",
  );
}

async function main() {
  const args = process.argv.slice(2);
  if (args.length < 3) {
    printUsage();
    process.exit(1);
  }

  const orgIdentifier = args[0];
  const role = args[1]!;
  const emails = args.slice(2);

  // Find organization by ID or slug
  const org = await prisma.organization.findFirst({
    where: { OR: [{ id: orgIdentifier }, { slug: orgIdentifier }] },
  });

  if (!org) {
    console.error(
      `❌ Organization not found for id/slug: ${orgIdentifier} — provide a valid organization id or slug`,
    );
    process.exit(1);
  }

  for (const email of emails) {
    // Find user by email
    const user = await prisma.user.findUnique({ where: { email } });
    if (!user) {
      console.warn(`⚠️ User not found for email: ${email}, skipping`);
      continue;
    }

    // Check existing membership
    const existing = await prisma.member.findFirst({
      where: { organizationId: org.id, userId: user.id },
    });
    if (existing) {
      console.log(
        `ℹ️ User ${email} (id=${user.id}) is already a member of organization ${org.id}, skipping`,
      );
      continue;
    }

    // Add member with specified role
    const member = await prisma.member.create({
      data: { organizationId: org.id, userId: user.id, role },
    });

    console.log(
      `✅ Added user ${email} (id=${user.id}) to organization ${org.id} as member id=${member.id}`,
    );
  }

  await prisma.$disconnect();
}

main().catch(async (e) => {
  console.error(e);
  await prisma.$disconnect();
  process.exit(1);
});
