#!/usr/bin/env bun

/**
 * <PERSON><PERSON>t to test Better Auth API directly
 * 
 * Usage:
 * bun run scripts/test-better-auth-api.ts
 */

async function testBetterAuthAPI() {
  try {
    console.log("🔍 Testing Better Auth API directly...\n");

    const { auth } = await import("../src/libs/auth");
    const { db } = await import("../src/server/db");

    // Get the user and organization we're testing with
    const userId = "czljsfg3eljdt6p9ymb5e70w"; // <EMAIL>
    const organizationId = "t8a4ftqwds9fc6g2bg6xo4zo"; // Taco Bell TeamLyders

    console.log(`Testing with:`);
    console.log(`  User ID: ${userId}`);
    console.log(`  Organization ID: ${organizationId}\n`);

    // Check the member record in the database
    const member = await db.member.findUnique({
      where: {
        member_userId_organizationId_key: {
          userId,
          organizationId,
        },
        deletedAt: null,
      },
      select: {
        id: true,
        role: true,
        user: {
          select: {
            name: true,
            email: true,
          },
        },
        organization: {
          select: {
            name: true,
            slug: true,
          },
        },
      },
    });

    if (!member) {
      console.log("❌ Member record not found in database");
      return;
    }

    console.log("✅ Member record found in database:");
    console.log(`   User: ${member.user.name} (${member.user.email})`);
    console.log(`   Organization: ${member.organization.name} (${member.organization.slug})`);
    console.log(`   Role: ${member.role}`);
    console.log(`   Member ID: ${member.id}\n`);

    // Now test Better Auth APIs
    console.log("🧪 Testing Better Auth APIs...\n");

    // Create mock headers (this is tricky without a real request)
    const mockHeaders = new Headers({
      'content-type': 'application/json',
      'user-agent': 'test-script',
    });

    try {
      // Test 1: Try to list organization roles
      console.log("1. Testing listOrgRoles...");
      const roles = await auth.api.listOrgRoles({
        query: { organizationId },
        headers: mockHeaders,
      });
      console.log("✅ listOrgRoles succeeded:", roles);
    } catch (error) {
      console.log("❌ listOrgRoles failed:", error);
    }

    try {
      // Test 2: Try to check permissions
      console.log("\n2. Testing hasPermission...");
      const hasPermission = await auth.api.hasPermission({
        headers: mockHeaders,
        body: {
          permissions: {
            ac: ["read"]
          },
          organizationId,
        },
      });
      console.log("✅ hasPermission succeeded:", hasPermission);
    } catch (error) {
      console.log("❌ hasPermission failed:", error);
    }

    console.log("\n🔧 The issue is likely that Better Auth APIs require a valid session");
    console.log("   The error you're seeing happens when the API is called from the browser");
    console.log("   with a valid session, but Better Auth doesn't recognize the admin role");

  } catch (error) {
    console.error("❌ Error testing Better Auth API:", error);
  } finally {
    const { db } = await import("../src/server/db");
    await db.$disconnect();
  }
}

testBetterAuthAPI().catch(console.error);
