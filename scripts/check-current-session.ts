#!/usr/bin/env bun

/**
 * <PERSON>ript to check which user is currently logged in
 * 
 * Usage:
 * bun run scripts/check-current-session.ts
 */

import { auth } from "../src/libs/auth";
import { headers } from "next/headers";

async function checkCurrentSession() {
  try {
    console.log("🔍 Checking current session...\n");

    // This won't work in a script context since we don't have request headers
    // Let's check the database for recent sessions instead
    
    const { db } = await import("../src/server/db");
    
    // Get recent active sessions
    const recentSessions = await db.session.findMany({
      where: {
        expiresAt: {
          gt: new Date(), // Only active sessions
        },
      },
      select: {
        id: true,
        userId: true,
        expiresAt: true,
        createdAt: true,
        updatedAt: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
      take: 10,
    });

    if (recentSessions.length === 0) {
      console.log("❌ No active sessions found");
      return;
    }

    console.log(`📋 Found ${recentSessions.length} active session(s):\n`);

    for (const session of recentSessions) {
      const user = session.user;
      console.log(`🔑 Session: ${session.id}`);
      console.log(`   User: ${user.name} (${user.email})`);
      console.log(`   User ID: ${user.id}`);
      console.log(`   Created: ${session.createdAt.toISOString()}`);
      console.log(`   Updated: ${session.updatedAt.toISOString()}`);
      console.log(`   Expires: ${session.expiresAt.toISOString()}`);
      
      // Check if this is one of your accounts
      if (user.email.includes('jalen')) {
        console.log(`   🎯 This appears to be your account!`);
        
        // Check their org memberships
        const members = await db.member.findMany({
          where: {
            userId: user.id,
            deletedAt: null,
          },
          select: {
            role: true,
            organization: {
              select: {
                name: true,
                slug: true,
                id: true,
              },
            },
          },
        });
        
        console.log(`   📋 Organization memberships:`);
        for (const member of members) {
          const org = member.organization;
          console.log(`      - ${org.name} (${org.slug}): ${member.role}`);
          if (org.id === 't8a4ftqwds9fc6g2bg6xo4zo') {
            console.log(`        🎯 This is the Taco Bell org where you're getting the error!`);
            if (member.role === 'admin') {
              console.log(`        ✅ You have admin access here`);
            } else {
              console.log(`        ❌ You only have ${member.role} access here`);
            }
          }
        }
      }
      
      console.log("");
    }

    console.log("🔧 Troubleshooting:");
    console.log("1. Make sure you're logged in with the correct account");
    console.log("2. If you need admin access, log <NAME_EMAIL>");
    console.log("3. Or update the <NAME_EMAIL> to admin");

  } catch (error) {
    console.error("❌ Error checking session:", error);
  } finally {
    const { db } = await import("../src/server/db");
    await db.$disconnect();
  }
}

checkCurrentSession().catch(console.error);
