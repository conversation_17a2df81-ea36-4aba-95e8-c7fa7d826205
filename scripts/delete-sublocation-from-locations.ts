import { PrismaClient } from "@prisma/client";
import readline from "readline";

const prisma = new PrismaClient();

function timestamp(): string {
  return new Date().toISOString();
}

function logInfo(msg: string) {
  console.log(`[${timestamp()}] INFO: ${msg}`);
}

function logWarn(msg: string) {
  console.warn(`[${timestamp()}] WARN: ${msg}`);
}

function logError(msg: string) {
  console.error(`[${timestamp()}] ERROR: ${msg}`);
}

function printUsage() {
  console.log(
    "Usage: bun run scripts/delete-sublocation-from-locations.ts <sublocationName>",
  );
  console.log("");
  console.log(
    "Deletes all sublocations with the exact name provided (across all locations).",
  );
  console.log(
    "You can provide multi-word names without quoting; the script will join remaining args.",
  );
  console.log("");
  console.log("Example:");
  console.log(
    "  bun run scripts/delete-sublocation-from-locations.ts Back of House",
  );
}

async function confirmPrompt(question: string): Promise<boolean> {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      rl.close();
      const normalized = (answer || "").trim().toLowerCase();
      resolve(normalized === "y" || normalized === "yes");
    });
  });
}

async function main() {
  const args = process.argv.slice(2);
  if (args.length === 0) {
    printUsage();
    process.exit(1);
  }

  const name = args.join(" ").trim();
  if (!name) {
    printUsage();
    process.exit(1);
  }

  logInfo(`Searching for sublocations with name: "${name}"`);

  const matches = await prisma.subLocation.findMany({
    where: { name },
    select: { id: true, locationId: true, shortId: true, name: true },
  });

  if (matches.length === 0) {
    logInfo(`No sublocations found with name "${name}".`);
    await prisma.$disconnect();
    return;
  }

  logInfo(`Found ${matches.length} sublocation(s) with name "${name}".`);

  // Fetch location names to provide better context in logs
  const locationIds = Array.from(new Set(matches.map((m) => m.locationId)));
  const locations = await prisma.location.findMany({
    where: { id: { in: locationIds } },
    select: { id: true, name: true, shortId: true },
  });
  const locationNameMap = new Map(locations.map((l) => [l.id, l.name]));

  // Group matches by location
  const grouped = new Map<string, typeof matches>();
  for (const m of matches) {
    if (!grouped.has(m.locationId))
      grouped.set(m.locationId, [] as typeof matches);
    grouped.get(m.locationId)!.push(m);
  }

  logInfo("Per-location summary:");
  for (const [locId, group] of grouped.entries()) {
    const locName = locationNameMap.get(locId) ?? "<unknown>";
    logInfo(
      `  - location ${locId} (${locName}): ${group.length} sublocation(s)`,
    );
    for (const g of group.slice(0, 5)) {
      logInfo(`      • id=${g.id} shortId=${g.shortId ?? "<none>"}`);
    }
    if (group.length > 5) logInfo(`      ...and ${group.length - 5} more`);
  }

  const ok = await confirmPrompt(
    "Delete these sublocations? Type 'yes' to confirm: ",
  );
  if (!ok) {
    logInfo("Aborted by user.");
    await prisma.$disconnect();
    return;
  }

  // perform deleteMany
  const res = await prisma.subLocation.deleteMany({ where: { name } });
  logInfo(`Deleted ${res.count} sublocation(s) with name "${name}".`);

  await prisma.$disconnect();
}

main().catch(async (e) => {
  console.error(e);
  await prisma.$disconnect();
  process.exit(1);
});
