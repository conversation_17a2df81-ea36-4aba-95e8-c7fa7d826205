import { PrismaClient } from "@prisma/client";
import { custom<PERSON><PERSON><PERSON><PERSON> } from "nanoid";

const prisma = new PrismaClient();

// 6-char, URL-safe alphabet (same as other scripts)
const nanoid = customAlphabet(
  "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",
  6,
);

// This script adds the same sublocation name(s) to every Location that belongs to
// the given organization. To keep things simple we don't support per-location mappings.

function drawProgressBar(completed: number, total: number, width = 40) {
  if (!process.stdout.isTTY) return; // avoid control chars when not a TTY
  const pct = total === 0 ? 1 : completed / total;
  const filled = Math.round(pct * width);
  const empty = width - filled;
  const bar = `[${"#".repeat(filled)}${"-".repeat(empty)}]`;
  const percentText = `${Math.round(pct * 100)
    .toString()
    .padStart(3)}%`;
  process.stdout.write(`\r${bar} ${percentText} (${completed}/${total})`);
}

async function ensureUniqueShortId(locationId: string): Promise<string> {
  let code: string;
  let tries = 0;

  do {
    if (++tries > 10)
      throw new Error(
        `Too many shortId collisions when generating for location ${locationId}`,
      );
    code = nanoid();
  } while (
    await prisma.subLocation.findFirst({ where: { locationId, shortId: code } })
  );

  return code;
}

async function addSublocationsToOrganization(
  orgIdentifier: string,
  globalNames: string[],
) {
  const org = await prisma.organization.findFirst({
    where: { OR: [{ id: orgIdentifier }, { slug: orgIdentifier }] },
  });
  if (!org) {
    console.error(
      `Organization not found for id/slug: ${orgIdentifier} — provide a valid organization id or slug`,
    );
    process.exit(1);
  }

  const locations = await prisma.location.findMany({
    where: { organizationId: org.id },
    select: { id: true, shortId: true, name: true },
  });

  if (locations.length === 0) {
    console.log(`No locations found for organization ${org.id}`);
    return;
  }
  await processLocations(locations, globalNames);
}

async function processLocations(
  locations: { id: string; shortId: string | null; name: string }[],
  globalNames: string[],
) {
  if (locations.length === 0) {
    console.log("No locations to process");
    return { created: 0, skipped: 0, locationsProcessed: 0 };
  }

  const locationIds = locations.map((l) => l.id);
  const names = Array.from(new Set(globalNames));

  console.log(
    `Preparing to add ${names.length} name(s) to ${locations.length} location(s) — total targets=${locations.length * names.length}`,
  );

  // Step B: prefetch existing sublocations for these locations and names
  const existingRows = await prisma.subLocation.findMany({
    where: {
      locationId: { in: locationIds },
      name: { in: names },
    },
    select: { locationId: true, name: true, shortId: true },
  });

  // Build map of existing names per location and existing shortIds per location
  const existingNameMap = new Map<string, Set<string>>();
  const existingShortIdMap = new Map<string, Set<string>>();
  for (const r of existingRows) {
    if (!existingNameMap.has(r.locationId))
      existingNameMap.set(r.locationId, new Set());
    existingNameMap.get(r.locationId)!.add(r.name);

    if (!existingShortIdMap.has(r.locationId))
      existingShortIdMap.set(r.locationId, new Set());
    if (r.shortId) existingShortIdMap.get(r.locationId)!.add(r.shortId);
  }

  const totalWanted = locations.length * names.length;
  const existingCount = existingRows.length;
  const missingCount = totalWanted - existingCount;

  console.log(
    `Existing sublocations matching criteria: ${existingCount}. Missing to create: ${missingCount}.`,
  );

  // Step C: Generate shortIds in memory for every missing (location, name) pair
  const rowsToInsert: { locationId: string; name: string; shortId: string }[] =
    [];
  for (const loc of locations) {
    const existingNames = existingNameMap.get(loc.id) ?? new Set<string>();
    const usedShortIds = existingShortIdMap.get(loc.id) ?? new Set<string>();

    for (const name of names) {
      if (existingNames.has(name)) continue; // already exists

      // generate unique shortId for this location
      let shortId: string;
      let tries = 0;
      do {
        if (++tries > 20)
          throw new Error(
            `Too many shortId collisions generating for location ${loc.id}`,
          );
        shortId = nanoid();
      } while (usedShortIds.has(shortId));

      // reserve it locally
      usedShortIds.add(shortId);
      rowsToInsert.push({ locationId: loc.id, name, shortId });
    }
  }

  console.log(
    `Prepared ${rowsToInsert.length} rows to insert (will batch with createMany).`,
  );

  // Step D: Insert missing rows in batches using createMany with skipDuplicates
  const BATCH_SIZE = 500;
  let insertedTotal = 0;
  for (let i = 0; i < rowsToInsert.length; i += BATCH_SIZE) {
    const batch = rowsToInsert.slice(i, i + BATCH_SIZE);
    try {
      // Re-check the DB for any (locationId,name) pairs that may have been created
      // since we first prefetched. We query by locationId and name sets and
      // filter to exact pairs.
      const batchLocationIds = Array.from(
        new Set(batch.map((b) => b.locationId)),
      );
      const batchNames = Array.from(new Set(batch.map((b) => b.name)));

      const existingInBatch = await prisma.subLocation.findMany({
        where: {
          locationId: { in: batchLocationIds },
          name: { in: batchNames },
        },
        select: { locationId: true, name: true },
      });

      const existingPairs = new Set(
        existingInBatch.map((r) => `${r.locationId}:::${r.name}`),
      );

      const filtered = batch.filter(
        (b) => !existingPairs.has(`${b.locationId}:::${b.name}`),
      );
      if (filtered.length === 0) {
        console.log(
          `Batch ${i / BATCH_SIZE + 1}: nothing to insert (all created already)`,
        );
        continue;
      }

      const res = await prisma.subLocation.createMany({
        data: filtered,
      });
      insertedTotal += res.count ?? filtered.length;
      console.log(
        `Inserted batch ${i / BATCH_SIZE + 1}: requested=${batch.length} inserted=${res.count ?? filtered.length} (filtered=${filtered.length})`,
      );
      // update progress bar
      drawProgressBar(
        Math.min(insertedTotal, rowsToInsert.length),
        rowsToInsert.length,
      );
    } catch (err) {
      console.error(`Error inserting batch starting at index ${i}:`, err);
      // continue with next batch
    }
  }

  // clear progress bar line if TTY
  if (process.stdout.isTTY) process.stdout.write("\n");

  const created = insertedTotal;
  const skippedExisting = existingCount;
  const skippedDuplicates = Math.max(0, missingCount - insertedTotal);
  const skipped = skippedExisting + skippedDuplicates;

  console.log(`\nDone. Total locations processed: ${locations.length}`);
  console.log(
    `Created: ${created}. Skipped (already existed): ${skippedExisting}. Skipped (duplicates at insert): ${skippedDuplicates}.`,
  );

  return { created, skipped, locationsProcessed: locations.length };
}

function printUsage() {
  console.log(
    "Usage: bun run scripts/add-sublocations-to-locations.ts <orgIdOrSlug> [namesCommaSeparated]",
  );
  console.log("");
  console.log("Arguments:");
  console.log("  <orgIdOrSlug>         Organization id or slug to target");
  console.log(
    "  [namesCommaSeparated] Optional comma-separated sublocation names to add to each store (defaults to 'Main')",
  );
  console.log("");
  console.log("Notes:");
  console.log(
    "  This script will add the same sublocation name(s) to every Location in the given organization.",
  );
  console.log("  It does not support per-location mappings.");
  console.log(
    "  You can pass multi-word names without quoting — the script will join remaining arguments and split on commas, semicolons, or pipes.",
  );
  console.log("");
  console.log("Examples:");
  console.log(
    "  bun run scripts/add-sublocations-to-locations.ts my-org-slug Main,Back",
  );
}

async function main() {
  const args = process.argv.slice(2);
  if (args.length === 0) {
    printUsage();
    process.exit(1);
  }

  const orgIdentifier = args[0] as string;

  // Join all remaining args so multi-word names (e.g. "Back of House") work even when not quoted.
  // Then split on common delimiters: comma, semicolon, or pipe.
  const namesArg = args.slice(1).join(" ");

  const globalNames =
    namesArg && namesArg.trim()
      ? namesArg
          .split(/[,;|]+/)
          .map((s) => s.trim())
          .filter(Boolean)
      : ["Main"];

  console.log(
    `Adding sublocations to organization ${orgIdentifier} — names=${globalNames.join(",")}`,
  );
  await addSublocationsToOrganization(orgIdentifier, globalNames);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(() => prisma.$disconnect());
