import { PrismaClient } from "@prisma/client";
import Stores from "./data/Stores_202508050956.json";

const prisma = new PrismaClient();

async function uploadStoreLocations() {
  const locations = Stores.Stores.map((store) => ({
    organizationId: "t8a4ftqwds9fc6g2bg6xo4zo",
    name: `${store["Brand"]} - ${store["City"]} - ${store["Store ID"]}`,
    address: store["Street Address"],
    city: store.City,
    state: store.State,
    country: "US",
    postalCode: store["Zip Code"].toString(),
  }));

  await prisma.location.createMany({
    data: locations,
  });
}

async function main() {
  console.log("Uploading store locations…");
  await uploadStoreLocations();
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(() => prisma.$disconnect());
