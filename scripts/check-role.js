#!/usr/bin/env node

/**
 * Simple script to check user's role in organizations
 * 
 * Usage: node scripts/check-role.js
 */

const { PrismaClient } = require('@prisma/client');

const db = new PrismaClient();

async function main() {
  try {
    console.log("🔍 Checking all users and their organization roles...\n");

    // Get all users with their organization memberships
    const users = await db.user.findMany({
      where: {
        deletedAt: null,
      },
      select: {
        id: true,
        name: true,
        email: true,
        members: {
          where: { deletedAt: null },
          select: {
            id: true,
            role: true,
            createdAt: true,
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        },
      },
    });

    if (users.length === 0) {
      console.log("❌ No users found");
      return;
    }

    console.log(`📋 Found ${users.length} users:\n`);

    for (const user of users) {
      console.log(`👤 ${user.name} (${user.email})`);
      console.log(`   User ID: ${user.id}`);
      
      if (user.members.length === 0) {
        console.log("   ❌ Not a member of any organizations");
      } else {
        console.log(`   📋 Member of ${user.members.length} organization(s):`);
        
        for (const member of user.members) {
          const org = member.organization;
          console.log(`      🏢 ${org.name} (${org.slug})`);
          console.log(`         Role: ${member.role}`);
          console.log(`         Org ID: ${org.id}`);
          console.log(`         Member ID: ${member.id}`);
          
          if (member.role === 'admin') {
            console.log(`         ✅ ADMIN ACCESS`);
          } else {
            console.log(`         ⚠️  Limited access (${member.role})`);
          }
        }
      }
      console.log("");
    }

    // Also check Better Auth dynamic roles
    console.log("🔧 Checking Better Auth dynamic roles...\n");
    
    const betterAuthRoles = await db.organizationRole.findMany({
      select: {
        id: true,
        role: true,
        permission: true,
        createdAt: true,
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    if (betterAuthRoles.length === 0) {
      console.log("❌ No Better Auth dynamic roles found");
    } else {
      console.log(`📝 Found ${betterAuthRoles.length} Better Auth dynamic role(s):`);
      
      for (const role of betterAuthRoles) {
        console.log(`   🎭 Role: ${role.role}`);
        console.log(`      Organization: ${role.organization.name} (${role.organization.slug})`);
        console.log(`      Created: ${role.createdAt.toISOString()}`);
        console.log(`      Permissions: ${JSON.stringify(role.permission, null, 6)}`);
        console.log("");
      }
    }

  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await db.$disconnect();
  }
}

main().catch(console.error);
