#!/usr/bin/env bun

/**
 * <PERSON>ript to debug what Better Auth is actually seeing for role resolution
 * 
 * Usage:
 * bun run scripts/debug-better-auth-role.ts
 */

async function debugBetterAuthRole() {
  try {
    console.log("🔍 Debugging Better Auth role resolution...\n");

    const { db } = await import("../src/server/db");

    const userId = "czljsfg3eljdt6p9ymb5e70w"; // <EMAIL>
    const organizationId = "t8a4ftqwds9fc6g2bg6xo4zo"; // Taco Bell TeamLyders

    console.log(`Debugging for:`);
    console.log(`  User ID: ${userId}`);
    console.log(`  Organization ID: ${organizationId}\n`);

    // Check ALL member records for this user
    console.log("=== ALL MEMBER RECORDS FOR USER ===");
    const allMembers = await db.member.findMany({
      where: {
        userId,
        deletedAt: null,
      },
      select: {
        id: true,
        role: true,
        organizationId: true,
        createdAt: true,
        updatedAt: true,
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    for (const member of allMembers) {
      const isTarget = member.organizationId === organizationId;
      const marker = isTarget ? "🎯" : "  ";
      
      console.log(`${marker} Member ID: ${member.id}`);
      console.log(`   Organization: ${member.organization.name} (${member.organization.slug})`);
      console.log(`   Organization ID: ${member.organizationId}`);
      console.log(`   Role: ${member.role}`);
      console.log(`   Created: ${member.createdAt.toISOString()}`);
      console.log(`   Updated: ${member.updatedAt.toISOString()}`);
      
      if (isTarget) {
        console.log(`   🔥 THIS IS THE TARGET ORGANIZATION!`);
      }
      console.log("");
    }

    // Check if there are any duplicate member records
    console.log("=== CHECKING FOR DUPLICATES ===");
    const duplicates = await db.member.findMany({
      where: {
        userId,
        organizationId,
        // Don't filter by deletedAt to see if there are soft-deleted records
      },
      select: {
        id: true,
        role: true,
        deletedAt: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    if (duplicates.length > 1) {
      console.log(`⚠️  Found ${duplicates.length} member records for this user/org combination:`);
      for (const dup of duplicates) {
        const status = dup.deletedAt ? "DELETED" : "ACTIVE";
        console.log(`   - ${dup.id}: ${dup.role} (${status}) - Created: ${dup.createdAt.toISOString()}`);
      }
    } else {
      console.log("✅ No duplicate member records found");
    }

    // Check the exact query that Better Auth might be using
    console.log("\n=== BETTER AUTH STYLE QUERY ===");
    const betterAuthMember = await db.member.findUnique({
      where: {
        member_userId_organizationId_key: {
          userId,
          organizationId,
        },
        deletedAt: null,
      },
      select: {
        id: true,
        role: true,
        userId: true,
        organizationId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (betterAuthMember) {
      console.log("✅ Better Auth style query found member:");
      console.log(`   Member ID: ${betterAuthMember.id}`);
      console.log(`   Role: ${betterAuthMember.role}`);
      console.log(`   User ID: ${betterAuthMember.userId}`);
      console.log(`   Organization ID: ${betterAuthMember.organizationId}`);
    } else {
      console.log("❌ Better Auth style query found NO member");
    }

    // Check if there might be a session/organization mismatch
    console.log("\n=== CHECKING ACTIVE SESSIONS ===");
    const sessions = await db.session.findMany({
      where: {
        userId,
        expiresAt: {
          gt: new Date(),
        },
      },
      select: {
        id: true,
        createdAt: true,
        updatedAt: true,
        expiresAt: true,
      },
      orderBy: {
        updatedAt: 'desc',
      },
      take: 3,
    });

    console.log(`Found ${sessions.length} active sessions for user`);
    for (const session of sessions) {
      console.log(`   Session: ${session.id} (updated: ${session.updatedAt.toISOString()})`);
    }

  } catch (error) {
    console.error("❌ Error debugging Better Auth role:", error);
  } finally {
    const { db } = await import("../src/server/db");
    await db.$disconnect();
  }
}

debugBetterAuthRole().catch(console.error);
