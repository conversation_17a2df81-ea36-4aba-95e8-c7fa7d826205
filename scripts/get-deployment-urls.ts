import { PrismaClient } from "@prisma/client";
import fs from "fs";
import path from "path";

// This script reads environment variables from the current process. If you
// need to load a .env file locally, run this script with a tool that loads
// .env (for example: `bun run` will load .env when configured) or set the
// variables in your shell before running.

const prisma = new PrismaClient();

function sanitizeFileName(name: string) {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/(^-|-$)/g, "");
}

function quoteCsv(value: string | null | undefined) {
  if (value === null || value === undefined) return "";
  const s = String(value);
  if (s.includes(",") || s.includes("\n") || s.includes('"')) {
    return '"' + s.replace(/"/g, '""') + '"';
  }
  return s;
}

async function main() {
  const argv = process.argv.slice(2);
  if (argv.length === 0 || argv.includes("--help") || argv.includes("-h")) {
    console.log(
      "Usage: bun run scripts/get-deployment-urls.ts <org-id-or-slug>",
    );
    console.log(
      "Finds all locations and sublocations for the organization and writes a CSV file.",
    );
    process.exit(0);
  }

  const orgArg = argv[0];

  // Try find organization by id first, then by slug
  const org =
    (await prisma.organization.findUnique({ where: { id: orgArg } })) ||
    (await prisma.organization.findUnique({ where: { slug: orgArg } }));

  if (!org) {
    console.error(`Organization not found for id/slug: ${orgArg}`);
    process.exit(2);
  }

  const baseUrl =
    process.env.APP_BASE_URL ||
    process.env.NEXT_PUBLIC_APP_BASE_URL ||
    "https://app.formbox.app";

  // fetch locations and their sublocations
  const locations = await prisma.location.findMany({
    where: { organizationId: org.id, deletedAt: null },
    include: { sublocations: { where: { deletedAt: null } } },
    orderBy: { name: "asc" },
  });

  if (!locations || locations.length === 0) {
    console.log(`No locations found for organization ${org.name} (${org.id})`);
    process.exit(0);
  }

  const safeName = sanitizeFileName(org.slug || org.name || org.id);
  // Ensure scripts/data exists and write output there
  const outDir = path.resolve(process.cwd(), "scripts", "data");
  try {
    fs.mkdirSync(outDir, { recursive: true });
  } catch (e) {
    // ignore - we'll surface errors when writing
  }
  const outFile = path.resolve(outDir, `${safeName}-deployment-urls.csv`);

  const header =
    [
      "location_name",
      "sublocation_name",
      "deployment_url",
      "location_id",
      "sublocation_id",
    ].join(",") + "\n";

  const rows: string[] = [header];

  for (const loc of locations) {
    const locToken = loc.shortId || loc.id;

    // Only output rows for sublocations. If a location has no sublocations,
    // it will be skipped (per request: no location-only deployment links).
    for (const sl of loc.sublocations ?? []) {
      const slToken = sl.shortId || sl.id;
      const url = `${baseUrl.replace(/\/$/, "")}/view?location=${encodeURIComponent(locToken)}&sub-location=${encodeURIComponent(slToken)}`;
      rows.push(
        [
          quoteCsv(loc.name),
          quoteCsv(sl.name),
          quoteCsv(url),
          quoteCsv(loc.shortId || loc.id),
          quoteCsv(sl.shortId || sl.id),
        ].join(",") + "\n",
      );
    }
  }

  fs.writeFileSync(outFile, rows.join(""), { encoding: "utf8" });
  console.log(`Wrote ${outFile} (${rows.length - 1} rows)`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(() => prisma.$disconnect());
