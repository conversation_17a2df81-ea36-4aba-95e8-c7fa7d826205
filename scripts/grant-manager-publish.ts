#!/usr/bin/env ts-node
/**
 * Seed utility to grant the 'publish' project permission to managers for a given organization.
 * Usage:
 *   bun run scripts/grant-manager-publish.ts <organizationId>
 */
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  const orgId = process.argv[2];
  if (!orgId) {
    console.error(
      "Organization ID required. Usage: bun run scripts/grant-manager-publish.ts <orgId>",
    );
    process.exit(1);
  }

  const org = await prisma.organization.findUnique({
    where: { id: orgId, deletedAt: null },
    select: { id: true, metadata: true },
  });

  if (!org) {
    console.error("Organization not found or deleted.");
    process.exit(1);
  }

  const meta: any = org.metadata || {};
  const overrides = meta.permissions?.overrides || {};
  const managerOverrides = overrides["manager"] || {};
  const publishSet = new Set(managerOverrides.project || []);
  publishSet.add("publish");
  managerOverrides.project = Array.from(publishSet);

  const newMeta = {
    ...meta,
    permissions: {
      ...(meta.permissions || {}),
      overrides: {
        ...overrides,
        manager: managerOverrides,
      },
    },
  };

  await prisma.organization.update({
    where: { id: org.id },
    data: { metadata: newMeta },
  });

  console.log(`Granted project.publish to managers for organization ${org.id}`);
  await prisma.$disconnect();
}

main().catch(async (e) => {
  console.error(e);
  await prisma.$disconnect();
  process.exit(1);
});
