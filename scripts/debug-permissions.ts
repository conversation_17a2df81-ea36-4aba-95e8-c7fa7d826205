#!/usr/bin/env bun

/**
 * Script to debug Better Auth permissions configuration
 * 
 * Usage:
 * bun run scripts/debug-permissions.ts
 */

async function debugPermissions() {
  try {
    console.log("🔍 Debugging Better Auth permissions configuration...\n");

    // Import the permissions
    const { 
      defaultStatements, 
      adminAc, 
      memberAc 
    } = await import("better-auth/plugins/organization/access");
    
    const { 
      ac, 
      admin, 
      manager, 
      member,
      getStatements 
    } = await import("../src/libs/auth-permissions");

    console.log("=== DEFAULT STATEMENTS FROM BETTER AUTH ===");
    console.log(JSON.stringify(defaultStatements, null, 2));
    console.log("");

    console.log("=== ADMIN AC STATEMENTS FROM BETTER AUTH ===");
    console.log(JSON.stringify(adminAc.statements, null, 2));
    console.log("");

    console.log("=== MEMBER AC STATEMENTS FROM BETTER AUTH ===");
    console.log(JSON.stringify(memberAc.statements, null, 2));
    console.log("");

    console.log("=== YOUR CURRENT STATEMENTS ===");
    const statements = getStatements();
    console.log(JSON.stringify(statements, null, 2));
    console.log("");

    console.log("=== YOUR ADMIN ROLE PERMISSIONS ===");
    // We can't directly access the role permissions, but we can check what's in the statements
    console.log("Admin role should include adminAc.statements plus custom permissions");
    console.log("");

    // Check if 'ac' resource is included
    if (defaultStatements.ac) {
      console.log("✅ 'ac' resource found in defaultStatements:", defaultStatements.ac);
    } else {
      console.log("❌ 'ac' resource NOT found in defaultStatements");
    }

    if (adminAc.statements.ac) {
      console.log("✅ 'ac' resource found in adminAc.statements:", adminAc.statements.ac);
    } else {
      console.log("❌ 'ac' resource NOT found in adminAc.statements");
    }

    if (statements.ac) {
      console.log("✅ 'ac' resource found in your statements:", statements.ac);
    } else {
      console.log("❌ 'ac' resource NOT found in your statements");
    }

    console.log("");
    console.log("🔧 Analysis:");
    console.log("The listOrgRoles operation requires 'ac' resource with 'read' permission");
    console.log("Your admin role needs to include this permission to list roles");

  } catch (error) {
    console.error("❌ Error debugging permissions:", error);
  }
}

debugPermissions().catch(console.error);
