#!/usr/bin/env bun

/**
 * <PERSON><PERSON><PERSON> to fix admin role for a user in an organization
 * 
 * Usage:
 * bun run scripts/fix-admin-role.ts [email] [orgSlug]
 * 
 * Examples:
 * bun run scripts/fix-admin-role.ts <EMAIL> my-org
 */

import { db } from "../src/server/db";

async function fixAdminRole(email?: string, orgSlug?: string) {
  try {
    // Get command line arguments
    const args = process.argv.slice(2);
    email = email || args[0];
    orgSlug = orgSlug || args[1];

    if (!email) {
      console.log("❌ Please provide an email address");
      console.log("Usage: bun run scripts/fix-admin-role.ts [email] [orgSlug]");
      return;
    }

    console.log(`🔍 Looking for user: ${email}\n`);

    // Find the user
    const user = await db.user.findUnique({
      where: { email },
      select: {
        id: true,
        name: true,
        email: true,
        members: {
          where: { deletedAt: null },
          select: {
            id: true,
            role: true,
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      console.log(`❌ User not found: ${email}`);
      return;
    }

    console.log(`✅ User found: ${user.name} (${user.email})`);

    if (user.members.length === 0) {
      console.log("❌ User is not a member of any organizations");
      return;
    }

    // If orgSlug is provided, find that specific organization
    let targetMember;
    if (orgSlug) {
      targetMember = user.members.find(m => m.organization.slug === orgSlug);
      if (!targetMember) {
        console.log(`❌ User is not a member of organization: ${orgSlug}`);
        console.log("Available organizations:");
        user.members.forEach(m => {
          console.log(`   - ${m.organization.name} (${m.organization.slug})`);
        });
        return;
      }
    } else {
      // If no orgSlug provided, use the first organization
      targetMember = user.members[0];
      console.log(`📋 No organization specified, using: ${targetMember.organization.name} (${targetMember.organization.slug})`);
    }

    console.log(`\n🏢 Organization: ${targetMember.organization.name}`);
    console.log(`   Slug: ${targetMember.organization.slug}`);
    console.log(`   Current role: ${targetMember.role}`);

    if (targetMember.role === 'admin') {
      console.log("✅ User already has admin role!");
      return;
    }

    // Update the role to admin
    console.log(`\n🔧 Updating role from '${targetMember.role}' to 'admin'...`);
    
    const updatedMember = await db.member.update({
      where: { id: targetMember.id },
      data: { role: 'admin' },
      select: {
        id: true,
        role: true,
        organization: {
          select: {
            name: true,
            slug: true,
          },
        },
      },
    });

    console.log("✅ Role updated successfully!");
    console.log(`   Member ID: ${updatedMember.id}`);
    console.log(`   New role: ${updatedMember.role}`);
    console.log(`   Organization: ${updatedMember.organization.name}`);

    console.log("\n🎉 User now has admin access to the organization!");
    console.log("Try the role listing operation again.");

  } catch (error) {
    console.error("❌ Error updating role:", error);
  } finally {
    await db.$disconnect();
  }
}

// Parse command line arguments and run
const args = process.argv.slice(2);
fixAdminRole(args[0], args[1]).catch(console.error);
